#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据流阻塞统一性深度审查脚本
确保三交易所完全统一，无重复，日志详细，架构一致
按照通用系统支持任意代币的核心理念进行审查
"""

import sys
import os
import re
import json
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set, Any

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class UnificationValidator:
    """统一性验证器"""
    
    def __init__(self):
        self.base_dir = Path("123")
        self.issues = []
        self.exchange_configs = {}
        self.blocking_systems = []
        
    def validate_blocking_detection_unification(self):
        """验证数据流阻塞检测的统一性"""
        
        print("🔍 深度审查数据流阻塞检测统一性...")
        
        # 1. 查找所有涉及阻塞检测的代码
        blocking_patterns = [
            'blocking', 'silent_duration', 'data_staleness', 
            '数据流阻塞', '数据过期', 'silent_disconnect',
            'update_symbol_data_flow', 'log_websocket_data_received'
        ]
        
        blocking_locations = defaultdict(list)
        
        for py_file in self.base_dir.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    for pattern in blocking_patterns:
                        if pattern in content:
                            # 统计出现次数和具体位置
                            matches = re.finditer(pattern, content)
                            for match in matches:
                                line_num = content[:match.start()].count('\n') + 1
                                blocking_locations[str(py_file)].append({
                                    'pattern': pattern,
                                    'line': line_num,
                                    'context': self._get_line_context(content, line_num)
                                })
            except:
                continue
        
        # 2. 分析重复和不一致
        print(f"\n📊 发现 {len(blocking_locations)} 个文件涉及阻塞检测:")
        
        duplicate_functions = {}
        inconsistent_thresholds = {}
        
        for file_path, matches in blocking_locations.items():
            print(f"\n  📄 {file_path}:")
            
            # 按模式分组
            pattern_groups = defaultdict(list)
            for match in matches:
                pattern_groups[match['pattern']].extend([match])
            
            for pattern, pattern_matches in pattern_groups.items():
                if len(pattern_matches) > 1:
                    duplicate_functions[file_path] = duplicate_functions.get(file_path, [])
                    duplicate_functions[file_path].append({
                        'pattern': pattern,
                        'count': len(pattern_matches),
                        'lines': [m['line'] for m in pattern_matches]
                    })
                
                print(f"    - {pattern}: {len(pattern_matches)}次")
        
        # 3. 检查重复的阻塞检测函数
        if duplicate_functions:
            print(f"\n❌ 发现重复的阻塞检测函数:")
            for file_path, duplicates in duplicate_functions.items():
                for dup in duplicates:
                    print(f"  {file_path}: {dup['pattern']} 出现{dup['count']}次 (行号: {dup['lines']})")
                    self.issues.append({
                        'type': '重复函数',
                        'severity': 'high',
                        'file': file_path,
                        'description': f"{dup['pattern']} 重复{dup['count']}次"
                    })
        
        return len(duplicate_functions) == 0
    
    def validate_exchange_consistency(self):
        """验证三交易所的一致性"""
        
        print("\n🔍 深度审查三交易所一致性...")
        
        ws_files = {
            'gate': 'websocket/gate_ws.py',
            'bybit': 'websocket/bybit_ws.py', 
            'okx': 'websocket/okx_ws.py'
        }
        
        exchange_implementations = {}
        
        # 提取关键配置和实现
        for exchange, file_path in ws_files.items():
            full_path = self.base_dir / file_path
            if not full_path.exists():
                continue
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取关键配置
            config = self._extract_key_configs(content, exchange)
            exchange_implementations[exchange] = config
        
        # 比较一致性
        inconsistencies = []
        
        # 检查阈值一致性
        thresholds = {}
        for exchange, config in exchange_implementations.items():
            thresholds[exchange] = config.get('thresholds', {})
        
        print(f"\n📊 交易所阈值对比:")
        for exchange, threshold_config in thresholds.items():
            print(f"  {exchange.upper()}:")
            for key, value in threshold_config.items():
                print(f"    {key}: {value}")
        
        # 检查阈值不一致
        threshold_keys = set()
        for config in thresholds.values():
            threshold_keys.update(config.keys())
        
        for key in threshold_keys:
            values = {}
            for exchange in thresholds:
                if key in thresholds[exchange]:
                    values[exchange] = thresholds[exchange][key]
            
            if len(set(values.values())) > 1:
                inconsistencies.append({
                    'type': '阈值不一致',
                    'key': key,
                    'values': values
                })
                print(f"    ❌ {key} 不一致: {values}")
        
        # 检查函数调用一致性
        key_functions = [
            'update_symbol_data_flow',
            '_log_data_received', 
            'timestamp_processor',
            'pool_manager'
        ]
        
        function_usage = {}
        for exchange, config in exchange_implementations.items():
            function_usage[exchange] = config.get('functions', [])
        
        print(f"\n📊 关键函数调用对比:")
        for func in key_functions:
            usage = {}
            for exchange in function_usage:
                usage[exchange] = func in function_usage[exchange]
            
            consistent = len(set(usage.values())) == 1
            status = "✅" if consistent else "❌"
            print(f"  {status} {func}: {usage}")
            
            if not consistent:
                inconsistencies.append({
                    'type': '函数调用不一致',
                    'function': func,
                    'usage': usage
                })
        
        if inconsistencies:
            print(f"\n❌ 发现 {len(inconsistencies)} 个一致性问题:")
            for issue in inconsistencies:
                print(f"  - {issue['type']}: {issue}")
                self.issues.append({
                    'type': '一致性问题',
                    'severity': 'high',
                    'description': f"{issue['type']}: {issue}"
                })
        
        return len(inconsistencies) == 0
    
    def validate_logging_detail(self):
        """验证日志详细程度"""
        
        print("\n🔍 深度审查日志详细程度...")
        
        # 检查enhanced_blocking_tracker的日志详细程度
        tracker_file = self.base_dir / "websocket/enhanced_blocking_tracker.py"
        if not tracker_file.exists():
            self.issues.append({
                'type': '缺失文件',
                'severity': 'critical',
                'description': 'enhanced_blocking_tracker.py 不存在'
            })
            return False
        
        with open(tracker_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键日志字段
        required_log_fields = [
            'silent_duration_seconds',
            'exchange',
            'market_type', 
            'symbol',
            'blocking_type',
            'cause',
            'severity',
            'context'
        ]
        
        missing_fields = []
        for field in required_log_fields:
            if field not in content:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 日志缺失关键字段: {missing_fields}")
            self.issues.append({
                'type': '日志不完整',
                'severity': 'high',
                'description': f'缺失字段: {missing_fields}'
            })
            return False
        
        # 检查日志格式的详细程度
        log_patterns = [
            r'self\.logger\.(info|warning|error|debug)\(',
            r'blocking_event.*=.*BlockingEvent\(',
            r'_log_blocking_event\(',
            r'json\.dumps.*asdict'
        ]
        
        detailed_logging = True
        for pattern in log_patterns:
            if not re.search(pattern, content):
                print(f"⚠️ 可能缺少详细日志模式: {pattern}")
                detailed_logging = False
        
        if detailed_logging:
            print("✅ 日志详细程度验证通过")
        
        return detailed_logging
    
    def validate_timestamp_processor_issues(self):
        """验证时间戳处理器的其他潜在问题"""
        
        print("\n🔍 深度审查时间戳处理器潜在问题...")
        
        processor_file = self.base_dir / "websocket/unified_timestamp_processor.py"
        if not processor_file.exists():
            return True
        
        with open(processor_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        potential_issues = []
        
        # 1. 检查硬编码值
        hardcoded_patterns = [
            r'\d{13}',  # 13位时间戳
            r'1754\d+',  # 特定的异常时间戳前缀
            r'time\.sleep\(\d+\)',  # 硬编码的sleep
        ]
        
        for pattern in hardcoded_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                potential_issues.append({
                    'type': '硬编码值',
                    'line': line_num,
                    'value': match.group(),
                    'context': self._get_line_context(content, line_num)
                })
        
        # 2. 检查交易所特定处理
        exchange_specific_patterns = [
            r'if.*exchange.*==.*["\']gate["\']',
            r'if.*exchange.*==.*["\']bybit["\']', 
            r'if.*exchange.*==.*["\']okx["\']',
            r'exchange_specific',
            r'gate.*特定',
            r'bybit.*特定',
            r'okx.*特定'
        ]
        
        for pattern in exchange_specific_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                potential_issues.append({
                    'type': '交易所特定处理',
                    'pattern': pattern,
                    'description': '可能违反通用系统原则'
                })
        
        # 3. 检查不一致的错误处理
        error_handling_patterns = [
            r'except.*Exception',
            r'except.*ImportError',
            r'except.*ValueError',
            r'except.*TypeError'
        ]
        
        error_handlers = []
        for pattern in error_handling_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                error_handlers.append({
                    'line': line_num,
                    'type': match.group(),
                    'context': self._get_line_context(content, line_num)
                })
        
        # 检查错误处理一致性
        error_handling_styles = set()
        for handler in error_handlers:
            # 提取错误处理风格
            context_lines = handler['context'].split('\n')
            for line in context_lines:
                if 'self.logger' in line:
                    if 'error' in line:
                        error_handling_styles.add('logger.error')
                    elif 'warning' in line:
                        error_handling_styles.add('logger.warning')
                    elif 'debug' in line:
                        error_handling_styles.add('logger.debug')
        
        if len(error_handling_styles) > 2:
            potential_issues.append({
                'type': '错误处理风格不一致',
                'styles': list(error_handling_styles),
                'description': '错误处理日志级别不统一'
            })
        
        # 输出发现的问题
        if potential_issues:
            print(f"⚠️ 时间戳处理器发现 {len(potential_issues)} 个潜在问题:")
            for issue in potential_issues:
                print(f"  - {issue['type']}: {issue.get('description', issue)}")
                self.issues.append({
                    'type': '时间戳处理器问题',
                    'severity': 'medium',
                    'description': f"{issue['type']}: {issue}"
                })
        else:
            print("✅ 时间戳处理器架构检查通过")
        
        return len(potential_issues) == 0
    
    def _extract_key_configs(self, content: str, exchange: str) -> Dict:
        """提取关键配置"""
        config = {
            'thresholds': {},
            'functions': [],
            'timeouts': {},
            'intervals': {}
        }
        
        # 提取阈值配置
        threshold_patterns = [
            (r'data_flow_timeout\s*=\s*(\d+)', 'data_flow_timeout'),
            (r'heartbeat_interval\s*=\s*(\d+)', 'heartbeat_interval'),
            (r'connection_timeout\s*=\s*(\d+)', 'connection_timeout'),
            (r'blocking_threshold\s*=\s*(\d+)', 'blocking_threshold'),
            (r'critical_threshold\s*=\s*(\d+)', 'critical_threshold'),
        ]
        
        for pattern, key in threshold_patterns:
            match = re.search(pattern, content)
            if match:
                config['thresholds'][key] = int(match.group(1))
        
        # 提取函数调用
        function_patterns = [
            'update_symbol_data_flow',
            '_log_data_received',
            'timestamp_processor',
            'pool_manager',
            'enhanced_blocking_tracker'
        ]
        
        for func in function_patterns:
            if func in content:
                config['functions'].append(func)
        
        return config
    
    def _get_line_context(self, content: str, line_num: int, context_lines: int = 2) -> str:
        """获取指定行的上下文"""
        lines = content.split('\n')
        start = max(0, line_num - context_lines - 1)
        end = min(len(lines), line_num + context_lines)
        
        context = []
        for i in range(start, end):
            prefix = ">>>" if i == line_num - 1 else "   "
            context.append(f"{prefix} {i+1:3d}: {lines[i]}")
        
        return '\n'.join(context)
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        
        print("\n" + "="*80)
        print("🎯 数据流阻塞统一性深度审查报告")
        print("="*80)
        
        # 执行所有验证
        blocking_unified = self.validate_blocking_detection_unification()
        exchange_consistent = self.validate_exchange_consistency()
        logging_detailed = self.validate_logging_detail()
        timestamp_clean = self.validate_timestamp_processor_issues()
        
        # 统计问题
        critical_issues = [i for i in self.issues if i.get('severity') == 'critical']
        high_issues = [i for i in self.issues if i.get('severity') == 'high']
        medium_issues = [i for i in self.issues if i.get('severity') == 'medium']
        
        print(f"\n📊 问题统计:")
        print(f"  🚨 关键问题: {len(critical_issues)}")
        print(f"  ❌ 高优先级: {len(high_issues)}")
        print(f"  ⚠️ 中优先级: {len(medium_issues)}")
        
        # 详细问题列表
        if self.issues:
            print(f"\n📋 详细问题列表:")
            for i, issue in enumerate(self.issues, 1):
                severity_emoji = {
                    'critical': '🚨',
                    'high': '❌', 
                    'medium': '⚠️',
                    'low': '💡'
                }
                emoji = severity_emoji.get(issue.get('severity', 'low'), '❓')
                print(f"  {i}. {emoji} {issue['type']}: {issue['description']}")
        
        # 验证结果
        print(f"\n🔍 验证结果:")
        print(f"  {'✅' if blocking_unified else '❌'} 数据流阻塞检测统一")
        print(f"  {'✅' if exchange_consistent else '❌'} 三交易所一致性")
        print(f"  {'✅' if logging_detailed else '❌'} 日志详细程度")
        print(f"  {'✅' if timestamp_clean else '❌'} 时间戳处理器架构")
        
        all_passed = (blocking_unified and exchange_consistent and 
                     logging_detailed and timestamp_clean)
        
        print(f"\n🎯 总体评估: {'✅ 完全统一' if all_passed else '❌ 需要修复'}")
        
        if not all_passed:
            print("\n💡 修复建议:")
            if not blocking_unified:
                print("  - 消除重复的阻塞检测函数")
            if not exchange_consistent:
                print("  - 统一三交易所的阈值和函数调用")
            if not logging_detailed:
                print("  - 完善日志详细程度和格式")
            if not timestamp_clean:
                print("  - 修复时间戳处理器的潜在问题")
        
        return all_passed

def main():
    """主函数"""
    
    print("🚀 数据流阻塞统一性深度审查开始")
    print("📋 按照通用系统支持任意代币的核心理念进行审查")
    print("🎯 确保差价精准性、三交易所一致性、高速性能")
    
    # 切换到正确的工作目录
    base_dir = Path(__file__).parent.parent
    if (base_dir / "123").exists():
        os.chdir(base_dir)
        print(f"📁 工作目录: {base_dir}")
    
    try:
        validator = UnificationValidator()
        all_unified = validator.generate_comprehensive_report()
        
        if all_unified:
            print("\n🎉 恭喜！数据流阻塞检测已完全统一，符合通用系统要求！")
        else:
            print("\n⚠️ 发现统一性问题，需要进一步修复")
            
    except Exception as e:
        print(f"❌ 审查过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()