#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳处理器架构问题精确诊断
专门诊断unified_timestamp_processor.py中的数据流阻塞误判问题
"""

import sys
import os
import time
import re
from datetime import datetime, timedelta
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_timestamp_processor_logic():
    """分析时间戳处理器的逻辑错误"""
    
    print("🔍 分析时间戳处理器架构问题...")
    
    # 读取unified_timestamp_processor.py
    processor_file = Path("123/websocket/unified_timestamp_processor.py")
    if not processor_file.exists():
        print(f"❌ 时间戳处理器文件不存在: {processor_file}")
        return
    
    with open(processor_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分析问题点
    issues_found = []
    
    # 1. 检查阈值错误
    threshold_pattern = r'time_diff > (\d+).*?# (\d+)秒'
    matches = re.findall(threshold_pattern, content)
    
    for code_value, comment_value in matches:
        if int(code_value) != int(comment_value) * 1000:
            issues_found.append({
                'type': '阈值不一致',
                'description': f'代码使用{code_value}ms，注释说{comment_value}秒',
                'severity': 'high'
            })
    
    # 2. 检查重复的阻塞检测逻辑
    blocking_checks = content.count('data_staleness_detected')
    if blocking_checks > 1:
        issues_found.append({
            'type': '重复检测',
            'description': f'发现{blocking_checks}处数据过期检测，可能导致重复报告',
            'severity': 'medium'
        })
    
    # 3. 检查时间同步状态处理
    unsync_pattern = r'时间未同步.*使用统一时间基准'
    if re.search(unsync_pattern, content):
        issues_found.append({
            'type': '未同步状态处理',
            'description': '时间戳未同步时使用统一时间基准可能导致误判',
            'severity': 'critical'
        })
    
    print(f"\n📊 发现 {len(issues_found)} 个潜在问题:")
    for i, issue in enumerate(issues_found, 1):
        severity_emoji = {'critical': '🚨', 'high': '❌', 'medium': '⚠️', 'low': '💡'}
        print(f"  {i}. {severity_emoji[issue['severity']]} {issue['type']}: {issue['description']}")
    
    return issues_found

def analyze_blocking_detection_architecture():
    """分析数据流阻塞检测的系统架构"""
    
    print("\n🏗️ 分析数据流阻塞检测架构...")
    
    # 找出所有涉及阻塞检测的文件
    blocking_files = []
    
    for py_file in Path("123").rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if any(keyword in content for keyword in [
                    'blocking', 'silent_duration', 'data_staleness', 
                    '数据流阻塞', '数据过期', 'silent_disconnect'
                ]):
                    blocking_files.append(py_file)
        except:
            continue
    
    print(f"📁 发现 {len(blocking_files)} 个文件涉及阻塞检测:")
    for file in blocking_files:
        print(f"  - {file}")
    
    # 分析架构冲突
    conflicts = []
    
    # 检查是否有多个系统在做相同的事情
    timestamp_processor = Path("123/websocket/unified_timestamp_processor.py")
    blocking_tracker = Path("123/websocket/enhanced_blocking_tracker.py")
    
    if timestamp_processor.exists() and blocking_tracker.exists():
        with open(timestamp_processor, 'r', encoding='utf-8') as f:
            ts_content = f.read()
        with open(blocking_tracker, 'r', encoding='utf-8') as f:
            bt_content = f.read()
        
        # 检查功能重叠
        if ('data_staleness_detected' in ts_content and 
            'data_staleness_detected' in bt_content):
            conflicts.append({
                'type': '功能重叠',
                'description': '时间戳处理器和阻塞追踪器都在检测数据过期',
                'files': ['unified_timestamp_processor.py', 'enhanced_blocking_tracker.py']
            })
    
    if conflicts:
        print(f"\n⚠️ 发现 {len(conflicts)} 个架构冲突:")
        for conflict in conflicts:
            print(f"  - {conflict['type']}: {conflict['description']}")
            print(f"    涉及文件: {', '.join(conflict['files'])}")
    
    return conflicts

def simulate_timestamp_processing_issue():
    """模拟时间戳处理导致的误判场景"""
    
    print("\n🧪 模拟时间戳处理误判场景...")
    
    # 模拟场景：时间戳未同步时的处理
    current_time = int(time.time() * 1000)  # 当前时间毫秒
    
    # 模拟统一时间基准（这个值看起来很异常）
    unified_base = 1754382297333  # 从日志中看到的值
    
    # 计算时间差
    time_diff = current_time - unified_base
    time_diff_seconds = time_diff / 1000
    
    print(f"📊 时间戳处理分析:")
    print(f"  当前时间: {current_time} ({datetime.fromtimestamp(current_time/1000)})")
    print(f"  统一基准: {unified_base} ({datetime.fromtimestamp(unified_base/1000)})")
    print(f"  时间差: {time_diff_seconds:.1f}秒")
    
    # 检查是否会触发阻塞检测
    if time_diff > 120000:  # 120秒阈值
        print(f"🚨 触发阻塞检测！时间差{time_diff_seconds:.1f}秒 > 120秒")
        print("   这可能是误判，因为统一时间基准异常")
    elif time_diff > 30000:  # 30秒阈值  
        print(f"⚠️ 接近阻塞阈值！时间差{time_diff_seconds:.1f}秒 > 30秒")
    else:
        print(f"✅ 时间差正常: {time_diff_seconds:.1f}秒")
    
    return time_diff_seconds

def check_websocket_integration_conflicts():
    """检查WebSocket集成冲突"""
    
    print("\n🔌 检查WebSocket集成冲突...")
    
    # 检查WebSocket客户端中的阻塞检测调用
    ws_files = [
        "123/websocket/gate_ws.py",
        "123/websocket/bybit_ws.py", 
        "123/websocket/okx_ws.py"
    ]
    
    integration_issues = []
    
    for ws_file in ws_files:
        if not Path(ws_file).exists():
            continue
            
        with open(ws_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否同时调用了多个监控系统
        calls = []
        if '_log_data_received' in content:
            calls.append('enhanced_blocking_tracker')
        if 'update_symbol_data_flow' in content:
            calls.append('connection_pool_manager')
        if 'timestamp_processor' in content:
            calls.append('timestamp_processor')
        
        if len(calls) > 2:
            integration_issues.append({
                'file': ws_file,
                'calls': calls,
                'issue': '多个监控系统可能冲突'
            })
    
    if integration_issues:
        print("⚠️ 发现WebSocket集成问题:")
        for issue in integration_issues:
            print(f"  {issue['file']}: {issue['issue']}")
            print(f"    调用的系统: {', '.join(issue['calls'])}")
    else:
        print("✅ WebSocket集成检查通过")
    
    return integration_issues

def main():
    """主诊断流程"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"🚀 时间戳处理器架构问题精确诊断 - {timestamp}")
    print("="*80)
    
    # 切换到正确的工作目录
    base_dir = Path(__file__).parent.parent
    if (base_dir / "123").exists():
        os.chdir(base_dir)
        print(f"📁 工作目录: {base_dir}")
    
    try:
        # 1. 分析时间戳处理器逻辑错误
        logic_issues = analyze_timestamp_processor_logic()
        
        # 2. 分析系统架构冲突
        arch_conflicts = analyze_blocking_detection_architecture()
        
        # 3. 模拟误判场景
        time_diff = simulate_timestamp_processing_issue()
        
        # 4. 检查WebSocket集成冲突
        integration_issues = check_websocket_integration_conflicts()
        
        print("\n" + "="*80)
        print("🎯 诊断结论:")
        
        if logic_issues:
            print("1. ❌ 时间戳处理器存在逻辑错误")
            for issue in logic_issues:
                if issue['severity'] == 'critical':
                    print(f"   🚨 关键问题: {issue['description']}")
        
        if arch_conflicts:
            print("2. ⚠️ 数据流阻塞检测架构存在冲突")
            
        if abs(time_diff) > 86400:  # 超过1天
            print("3. 🚨 统一时间基准异常，可能导致大量误判") 
            
        if integration_issues:
            print("4. ⚠️ WebSocket集成存在潜在冲突")
        
        print("\n💡 建议:")
        print("- 修复时间戳处理器的阈值错误")
        print("- 统一数据流阻塞检测，避免重复逻辑")
        print("- 修正统一时间基准的计算方式")
        print("- 简化WebSocket监控系统集成")
        
    except Exception as e:
        print(f"❌ 诊断过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()