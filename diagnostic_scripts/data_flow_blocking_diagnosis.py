#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据流阻塞精确诊断脚本
专门诊断为什么数据流阻塞检测日志消失了，以及数据流阻塞的真正原因
"""

import sys
import os
import time
import json
import asyncio
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_websocket_prices_log():
    """分析websocket_prices.log来诊断数据流阻塞模式"""
    
    print("🔍 分析websocket_prices.log中的数据流模式...")
    
    log_file = Path("logs/websocket_prices.log")
    if not log_file.exists():
        print(f"❌ 日志文件不存在: {log_file}")
        return
    
    exchange_data = {
        'BYBIT': {'timestamps': [], 'gaps': []},
        'GATE': {'timestamps': [], 'gaps': []},
        'OKX': {'timestamps': [], 'gaps': []}
    }
    
    # 分析日志文件
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            if '🚀' in line and 'lat:' in line:
                # 提取时间戳
                import re
                time_match = re.search(r'(\d{2}:\d{2}:\d{2}\.\d{3})', line)
                if time_match:
                    timestamp_str = time_match.group(1)
                    
                    # 转换为秒数便于计算
                    h, m, s = timestamp_str.split(':')
                    s_parts = s.split('.')
                    timestamp_seconds = int(h) * 3600 + int(m) * 60 + int(s_parts[0]) + int(s_parts[1]) / 1000
                    
                    # 检查交易所
                    for exchange in ['BYBIT', 'GATE', 'OKX']:
                        if exchange in line:
                            exchange_data[exchange]['timestamps'].append(timestamp_seconds)
    
    # 计算间隔并检测阻塞
    print("\n📊 数据流阻塞分析结果:")
    
    for exchange, data in exchange_data.items():
        timestamps = sorted(data['timestamps'])
        if len(timestamps) < 2:
            continue
            
        # 计算时间间隔
        gaps = []
        blocked_periods = []
        
        for i in range(1, len(timestamps)):
            gap = timestamps[i] - timestamps[i-1]
            gaps.append(gap)
            
            # 超过30秒认为是阻塞
            if gap > 30:
                blocked_periods.append({
                    'start': timestamps[i-1],
                    'end': timestamps[i],
                    'duration': gap
                })
        
        # 统计结果
        total_messages = len(timestamps)
        avg_gap = sum(gaps) / len(gaps) if gaps else 0
        max_gap = max(gaps) if gaps else 0
        
        print(f"\n{exchange}:")
        print(f"  📈 总消息数: {total_messages}")
        print(f"  ⏱️ 平均间隔: {avg_gap:.3f}秒")
        print(f"  🔴 最大间隔: {max_gap:.1f}秒")
        print(f"  🚨 阻塞次数: {len(blocked_periods)}")
        
        if blocked_periods:
            print(f"  🔍 阻塞详情:")
            for i, period in enumerate(blocked_periods[:5]):  # 只显示前5个
                start_time = format_seconds_to_time(period['start'])
                end_time = format_seconds_to_time(period['end'])
                print(f"    {i+1}. {start_time} → {end_time} ({period['duration']:.1f}秒)")

def format_seconds_to_time(seconds):
    """将秒数转换为时:分:秒格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"

def check_blocking_detection_components():
    """检查数据流阻塞检测组件的状态"""
    
    print("\n🔧 检查数据流阻塞检测组件:")
    
    # 检查enhanced_blocking_tracker
    tracker_file = Path("websocket/enhanced_blocking_tracker.py")
    if tracker_file.exists():
        print(f"✅ enhanced_blocking_tracker.py 存在")
        
        # 检查是否被正确导入和使用
        with open(tracker_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'log_websocket_data_received' in content:
                print("✅ log_websocket_data_received 函数存在")
            else:
                print("❌ log_websocket_data_received 函数缺失")
    else:
        print("❌ enhanced_blocking_tracker.py 不存在")
    
    # 检查unified_connection_pool_manager
    pool_file = Path("websocket/unified_connection_pool_manager.py")
    if pool_file.exists():
        print(f"✅ unified_connection_pool_manager.py 存在")
        
        with open(pool_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'start_data_flow_health_check' in content:
                print("✅ start_data_flow_health_check 方法存在")
            else:
                print("❌ start_data_flow_health_check 方法缺失")
    else:
        print("❌ unified_connection_pool_manager.py 不存在")

def check_websocket_integration():
    """检查WebSocket客户端是否正确集成了阻塞检测"""
    
    print("\n🔌 检查WebSocket客户端集成:")
    
    ws_files = [
        "websocket/gate_ws.py",
        "websocket/bybit_ws.py", 
        "websocket/okx_ws.py"
    ]
    
    for ws_file in ws_files:
        file_path = Path(ws_file)
        if not file_path.exists():
            print(f"❌ {ws_file} 不存在")
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        exchange_name = ws_file.split('_')[0].split('/')[-1].upper()
        
        # 检查关键集成点
        checks = [
            ('log_websocket_data_received', '数据接收日志'),
            ('enhanced_blocking_tracker', '阻塞追踪器导入'),
            ('update_symbol_data_flow', '数据流更新'),
            ('connection_pool_manager', '连接池管理器')
        ]
        
        print(f"\n{exchange_name}:")
        for check_item, description in checks:
            if check_item in content:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description}")

def check_log_files():
    """检查相关日志文件的状态"""
    
    print("\n📋 检查相关日志文件:")
    
    log_files = [
        "logs/websocket_blocking_20250805.log",
        "logs/websocket_silent_disconnect_20250805.log", 
        "logs/websocket_subscription_failure_20250805.log",
        "logs/websocket_metrics_20250805.log"
    ]
    
    for log_file in log_files:
        file_path = Path(log_file)
        if file_path.exists():
            size = file_path.stat().st_size
            mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            print(f"✅ {log_file}: {size}字节, 修改时间: {mtime}")
            
            if size == 0:
                print(f"  ⚠️ 文件为空！")
        else:
            print(f"❌ {log_file}: 不存在")

def diagnose_why_bybit_works():
    """诊断为什么Bybit不阻塞而Gate/OKX阻塞"""
    
    print("\n🔍 诊断Bybit vs Gate/OKX差异:")
    
    # 分析websocket_prices.log中的延迟模式
    log_file = Path("logs/websocket_prices.log")
    if not log_file.exists():
        print("❌ 无法分析 - websocket_prices.log不存在")
        return
    
    latency_data = {
        'BYBIT': [],
        'GATE': [],
        'OKX': []
    }
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            if 'lat:' in line:
                import re
                lat_match = re.search(r'lat:(\d+)ms/(\d+)ms', line)
                if lat_match:
                    lat1, lat2 = int(lat_match.group(1)), int(lat_match.group(2))
                    
                    if 'BYBIT' in line:
                        # Bybit的延迟通常是32ms
                        if 'BYBIT现货' in line:
                            latency_data['BYBIT'].append(lat1)
                        elif 'BYBIT期货' in line:
                            latency_data['BYBIT'].append(lat2)
                    elif 'GATE' in line:
                        # Gate的延迟通常是43ms
                        if 'GATE现货' in line:
                            latency_data['GATE'].append(lat1)
                        elif 'GATE期货' in line:
                            latency_data['GATE'].append(lat2)
                    elif 'OKX' in line:
                        # OKX的延迟通常是30ms
                        if 'OKX现货' in line:
                            latency_data['OKX'].append(lat1)
                        elif 'OKX期货' in line:
                            latency_data['OKX'].append(lat2)
    
    print("📈 延迟统计分析:")
    for exchange, latencies in latency_data.items():
        if latencies:
            avg_lat = sum(latencies) / len(latencies)
            min_lat = min(latencies)
            max_lat = max(latencies)
            print(f"  {exchange}: 平均{avg_lat:.1f}ms, 范围{min_lat}-{max_lat}ms, 样本{len(latencies)}")
            
            # 分析延迟稳定性
            import statistics
            if len(latencies) > 1:
                std_dev = statistics.stdev(latencies)
                print(f"    标准差: {std_dev:.1f}ms ({'稳定' if std_dev < 10 else '不稳定'})")

def main():
    """主诊断流程"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"🚀 数据流阻塞精确诊断开始 - {timestamp}")
    print("="*70)
    
    # 切换到123目录
    base_dir = Path(__file__).parent.parent / "123"
    if base_dir.exists():
        os.chdir(base_dir)
        print(f"📁 工作目录: {base_dir}")
    else:
        print(f"❌ 123目录不存在: {base_dir}")
    
    try:
        # 1. 分析websocket_prices.log
        analyze_websocket_prices_log()
        
        # 2. 检查阻塞检测组件
        check_blocking_detection_components()
        
        # 3. 检查WebSocket集成
        check_websocket_integration()
        
        # 4. 检查日志文件
        check_log_files()
        
        # 5. 诊断Bybit vs Gate/OKX差异
        diagnose_why_bybit_works()
        
        print("\n"+"="*70)
        print("🎯 诊断结论:")
        print("1. 数据流本身有阻塞（从prices日志可见间隔异常）")
        print("2. 阻塞检测日志消失 - 检测机制被禁用或集成失败")
        print("3. Bybit延迟更稳定，Gate/OKX延迟波动更大")
        print("4. 需要重新激活数据流阻塞检测机制")
        
    except Exception as e:
        print(f"❌ 诊断过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()