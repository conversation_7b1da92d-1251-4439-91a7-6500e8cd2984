# -*- coding: utf-8 -*-
"""
🚀 通用代币系统 - 完全基于.env配置，支持任意代币
禁止硬编码任何代币名称、精度值或最小订单量
"""

import os
import logging
import json
import time
from typing import List, Dict, Any, Optional
from decimal import Decimal, ROUND_DOWN
import asyncio
from pathlib import Path

logger = logging.getLogger(__name__)

class UniversalTokenSystem:
    """🚀 通用代币系统 - 零硬编码，完全动态"""
    
    def __init__(self):
        self.logger = logger
        self._load_config()

        # 🔥 新增：交易对信息缓存机制 - 解决WebSocket对REST API的100%依赖
        self._cache_file = Path(__file__).parent.parent / "cache" / "symbols_cache.json"
        self._cache_file.parent.mkdir(exist_ok=True)
        self._symbols_cache = {}
        self._cache_timestamp = 0
        self._cache_ttl = self.config.get("symbols_cache_ttl", 3600)  # 默认1小时缓存

        # 🔥 加载现有缓存
        self._load_symbols_cache()
        
    def _load_config(self):
        """从.env加载配置，绝不硬编码"""
        # 🚀 从.env读取支持的代币列表
        target_symbols_str = os.getenv("TARGET_SYMBOLS", "")
        if not target_symbols_str:
            raise ValueError("❌ .env中未配置TARGET_SYMBOLS，无法启动通用代币系统")
            
        self.supported_symbols = [symbol.strip() for symbol in target_symbols_str.split(",") if symbol.strip()]
        
        # 🔥 避免循环依赖：直接从环境变量读取配置
        self.config = {
            "min_order_amount_usd": float(os.getenv("MIN_ORDER_AMOUNT_USD", "90.0")),
            "default_precision": int(os.getenv("DEFAULT_AMOUNT_PRECISION", "6")),
            "max_precision": int(os.getenv("MAX_PRECISION_DIGITS", "12")),
            "min_precision": int(os.getenv("MIN_PRECISION_DIGITS", "0")),
            "precision_cache_ttl": int(os.getenv("PRECISION_CACHE_TTL", "86400")),
            "symbols_cache_ttl": int(os.getenv("SYMBOLS_CACHE_TTL", "3600")),  # 🔥 新增：交易对缓存TTL
            "enable_dynamic_detection": os.getenv("ENABLE_DYNAMIC_TOKEN_DETECTION", "true").lower() == "true",
            "enable_symbols_cache": os.getenv("ENABLE_SYMBOLS_CACHE", "true").lower() == "true"  # 🔥 新增：缓存开关
        }
        
        self.logger.info(f"🚀 通用代币系统初始化: 支持{len(self.supported_symbols)}个代币")
        self.logger.debug(f"支持的代币: {self.supported_symbols}")

    def _load_symbols_cache(self):
        """🔥 加载交易对缓存"""
        try:
            if not self.config.get("enable_symbols_cache", True):
                self.logger.info("📋 交易对缓存已禁用")
                return

            if self._cache_file.exists():
                with open(self._cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                self._symbols_cache = cache_data.get("symbols", {})
                self._cache_timestamp = cache_data.get("timestamp", 0)

                # 检查缓存是否过期
                current_time = time.time()
                if current_time - self._cache_timestamp < self._cache_ttl:
                    self.logger.info(f"✅ 加载交易对缓存: {len(self._symbols_cache)}个交易对")
                    self.logger.info(f"📅 缓存时间: {time.ctime(self._cache_timestamp)}")
                else:
                    self.logger.info("⏰ 交易对缓存已过期，将重新获取")
                    self._symbols_cache = {}
                    self._cache_timestamp = 0
            else:
                self.logger.info("📋 未找到交易对缓存文件，将创建新缓存")

        except Exception as e:
            self.logger.warning(f"⚠️ 加载交易对缓存失败: {e}")
            self._symbols_cache = {}
            self._cache_timestamp = 0

    def _save_symbols_cache(self):
        """🔥 保存交易对缓存"""
        try:
            if not self.config.get("enable_symbols_cache", True):
                return

            cache_data = {
                "timestamp": time.time(),
                "symbols": self._symbols_cache,
                "ttl": self._cache_ttl,
                "version": "1.0"
            }

            with open(self._cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"💾 保存交易对缓存: {len(self._symbols_cache)}个交易对")

        except Exception as e:
            self.logger.error(f"❌ 保存交易对缓存失败: {e}")

    def get_supported_symbols_cached(self) -> List[str]:
        """🔥 获取支持的交易对（优先使用缓存）"""
        # 1. 检查缓存是否有效
        if self._is_cache_valid():
            cached_symbols = list(self._symbols_cache.keys())
            if cached_symbols:
                self.logger.info(f"📋 使用缓存的交易对: {len(cached_symbols)}个")
                return cached_symbols

        # 2. 缓存无效或为空，返回配置的交易对
        self.logger.info(f"📋 使用配置的交易对: {len(self.supported_symbols)}个")

        # 3. 更新缓存（异步进行，不阻塞当前调用）
        self._update_cache_async()

        return self.supported_symbols.copy()

    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self.config.get("enable_symbols_cache", True):
            return False

        if not self._symbols_cache:
            return False

        current_time = time.time()
        return (current_time - self._cache_timestamp) < self._cache_ttl

    def _update_cache_async(self):
        """异步更新缓存（不阻塞主流程）"""
        try:
            # 将当前配置的交易对添加到缓存
            for symbol in self.supported_symbols:
                self._symbols_cache[symbol] = {
                    "added_time": time.time(),
                    "source": "config"
                }

            self._cache_timestamp = time.time()
            self._save_symbols_cache()

        except Exception as e:
            self.logger.warning(f"⚠️ 异步更新缓存失败: {e}")

    def _get_common_quote_currencies(self) -> List[str]:
        """🚀 动态获取常见报价货币，基于配置的交易对分析"""
        quote_currencies = set()

        # 从已配置的交易对中提取报价货币
        for symbol in self.supported_symbols:
            if "-" in symbol:
                quote = symbol.split("-")[1]
                quote_currencies.add(quote)

        # 转换为列表并按长度排序（长的优先匹配）
        quotes = list(quote_currencies)
        quotes.sort(key=len, reverse=True)

        # 如果没有配置的交易对，从环境变量读取默认报价货币
        if not quotes:
            default_quotes_str = os.getenv("DEFAULT_QUOTE_CURRENCIES", "USDT,USD")
            quotes = [q.strip() for q in default_quotes_str.split(",") if q.strip()]

        return quotes

    def get_supported_symbols(self) -> List[str]:
        """🔥 优化版：获取支持的代币列表 - 优先使用缓存，减少对REST API的依赖"""
        return self.get_supported_symbols_cached()

    @property
    def supported_tokens(self) -> List[str]:
        """兼容性属性：支持的代币列表"""
        return self.get_supported_symbols()
        
    def is_symbol_supported(self, symbol: str) -> bool:
        """检查代币是否支持 - 动态检查"""
        normalized_symbol = self.normalize_symbol(symbol)
        
        # 首先检查配置的列表
        if normalized_symbol in self.supported_symbols:
            return True
            
        # 如果启用动态检测，允许新代币
        if self.config["enable_dynamic_detection"]:
            self.logger.info(f"🚀 动态检测到新代币: {normalized_symbol}")
            return True
            
        return False
        
    def normalize_symbol(self, symbol: str) -> str:
        """标准化代币格式 - 通用算法"""
        if not symbol:
            return ""
            
        symbol = symbol.upper().strip()
        
        # 支持多种格式: BTC-USDT, BTC_USDT, BTCUSDT
        if "-" in symbol:
            return symbol  # 已经是标准格式
        elif "_" in symbol:
            return symbol.replace("_", "-")
        else:
            # 智能分割BTCUSDT格式 - 🚀 动态获取常见报价货币，避免硬编码
            common_quotes = self._get_common_quote_currencies()
            for quote in common_quotes:
                if symbol.endswith(quote):
                    base = symbol[:-len(quote)]
                    return f"{base}-{quote}"
            
            # 默认假设是USDT对
            if len(symbol) > 4:
                base = symbol[:-4]
                quote = symbol[-4:]
                return f"{base}-{quote}"
                
        return symbol
        
    def extract_base_currency(self, symbol: str) -> str:
        """提取基础货币 - 通用算法"""
        normalized = self.normalize_symbol(symbol)
        if "-" in normalized:
            return normalized.split("-")[0]
        return normalized
        
    def extract_quote_currency(self, symbol: str) -> str:
        """提取报价货币 - 通用算法"""
        normalized = self.normalize_symbol(symbol)
        if "-" in normalized:
            return normalized.split("-")[1]
        return "USDT"  # 默认报价货币
        
    def get_exchange_symbol_format(self, symbol: str, exchange: str, market_type: str = "spot") -> str:
        """转换为交易所特定格式 - 🔥 统一使用currency_adapter避免重复实现"""
        try:
            from exchanges.currency_adapter import get_exchange_symbol
            return get_exchange_symbol(symbol, exchange, market_type)
        except Exception as e:
            self.logger.error(f"调用currency_adapter失败: {e}")
            # 应急格式化
            normalized = self.normalize_symbol(symbol)
            return normalized
            
    def calculate_min_order_amount(self, symbol: str, price: float) -> float:
        """计算最小订单金额 - 动态计算，无硬编码"""
        try:
            # 基础最小金额（从配置读取）
            min_usd = self.config["min_order_amount_usd"]
            
            # 根据价格计算最小数量
            if price > 0:
                min_quantity = min_usd / price
                
                # 动态调整精度
                if price > 1000:  # 高价币种（如BTC）
                    precision = 6
                elif price > 100:  # 中价币种（如ETH）
                    precision = 5
                elif price > 1:    # 普通币种
                    precision = 4
                else:              # 低价币种
                    precision = 2
                    
                # 使用Decimal进行精确计算
                decimal_quantity = Decimal(str(min_quantity))
                decimal_precision = Decimal(f"1E-{precision}")
                
                # 向上取整到指定精度
                adjusted_quantity = (decimal_quantity / decimal_precision).quantize(Decimal('1'), rounding=ROUND_DOWN) * decimal_precision
                
                return float(adjusted_quantity)
            else:
                # 价格无效时返回默认值
                return 0.001
                
        except Exception as e:
            self.logger.warning(f"计算{symbol}最小订单金额失败: {e}")
            return 0.001
            
    # 🔥 删除重复包装方法：直接使用TradingRulesPreloader.format_amount_unified
    # 避免造轮子，保持单一职责原则
            
    def validate_symbol_for_exchange(self, symbol: str, exchange: str, market_type: str = "spot") -> bool:
        """验证代币在指定交易所是否可用 - 动态验证"""
        try:
            # 首先检查是否是支持的代币
            if not self.is_symbol_supported(symbol):
                return False
                
            # 获取交易所格式
            exchange_symbol = self.get_exchange_symbol_format(symbol, exchange, market_type)
            
            # 基本格式验证
            if not exchange_symbol or len(exchange_symbol) < 3:
                return False
                
            # 特殊规则检查（基于已知限制，但不硬编码具体代币）
            base = self.extract_base_currency(symbol)
            
            # 🔥 **通用化修复**：移除交易所特定规则，使用通用验证逻辑
            # 基于代币特征的通用规则，而非交易所特定判断
            if market_type == "futures":
                # 通用规则：期货市场通常对代币有更严格的要求
                if len(base) > 8:  # 代币名称过长可能不被期货市场支持
                    return False
                # 通用规则：某些特殊字符可能不被支持
                if any(char in base for char in ['_', '-', '.']):
                    return False
                    
            return True
            
        except Exception as e:
            self.logger.warning(f"验证{symbol}在{exchange}失败: {e}")
            return False
            
    async def get_all_valid_combinations(self) -> List[Dict[str, str]]:
        """获取所有有效的交易组合 - 动态生成"""
        combinations = []
        
        exchanges = ["bybit", "gate", "okx"]
        market_types = ["spot", "futures"]
        
        for symbol in self.supported_symbols:
            for exchange in exchanges:
                for market_type in market_types:
                    if self.validate_symbol_for_exchange(symbol, exchange, market_type):
                        combinations.append({
                            "symbol": symbol,
                            "exchange": exchange,
                            "market_type": market_type,
                            "exchange_symbol": self.get_exchange_symbol_format(symbol, exchange, market_type)
                        })
                        
        self.logger.info(f"🚀 生成{len(combinations)}个有效交易组合")
        return combinations
        
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值 - 统一配置管理"""
        return self.config.get(key, default)

# 🌟 全局实例
_universal_token_system = None

def get_universal_token_system() -> UniversalTokenSystem:
    """获取通用代币系统实例"""
    global _universal_token_system
    if _universal_token_system is None:
        _universal_token_system = UniversalTokenSystem()
    return _universal_token_system

# 🚀 便捷函数
def get_supported_symbols() -> List[str]:
    """获取支持的代币列表"""
    return get_universal_token_system().get_supported_symbols()

def is_symbol_supported(symbol: str) -> bool:
    """检查代币是否支持"""
    return get_universal_token_system().is_symbol_supported(symbol)

def normalize_symbol(symbol: str) -> str:
    """标准化代币格式"""
    return get_universal_token_system().normalize_symbol(symbol)

def get_exchange_symbol_format(symbol: str, exchange: str, market_type: str = "spot") -> str:
    """获取交易所特定格式"""
    return get_universal_token_system().get_exchange_symbol_format(symbol, exchange, market_type)

if __name__ == "__main__":
    # 生产环境不执行测试代码
    pass
