#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
套利引擎核心模块 - 负责协调整个套利流程
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import threading
from decimal import Decimal

from config.settings import get_config
from utils.logger import get_logger
from utils.helpers import format_currency, calculate_percentage
from fund_management.fund_manager import FundManager
from core.execution_engine import ExecutionEngine
from monitoring.position_monitor import PositionMonitor
from monitoring.risk_monitor import RiskMonitor
from exchanges.exchanges_base import OrderSide, OrderType
from core.opportunity_scanner import ArbitrageOpportunity
# 🔥 6大缓存监控系统
from utils.cache_monitor import log_balance_hit, log_balance_miss, log_balance_update


class ArbitrageStatus(Enum):
    """套利状态枚举"""
    IDLE = "idle"
    SCANNING = "scanning"
    PREPARING = "preparing"
    EXECUTING = "executing"
    WAITING_CONVERGENCE = "waiting_convergence"
    CLOSING = "closing"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class ArbitrageSession:
    """套利会话数据"""
    session_id: str
    opportunity: ArbitrageOpportunity
    status: ArbitrageStatus
    start_time: float
    spot_orders: List[Dict] = None
    futures_orders: List[Dict] = None
    total_spot_filled: float = 0.0
    total_futures_filled: float = 0.0
    realized_profit: float = 0.0
    end_time: Optional[float] = None


class ArbitrageEngine:
    """套利引擎核心类"""

    def __init__(self, config=None):
        self.config = config or get_config()
        self.logger = get_logger(self.__class__.__name__)

        # 核心组件 - 延迟初始化避免循环依赖
        self.fund_manager = None  # 在initialize时初始化
        self.opportunity_scanner = None  # 延迟初始化
        self.execution_engine = None  # 在initialize时初始化
        self.position_monitor = None  # 在initialize时初始化
        self.risk_monitor = None  # 在initialize时初始化
        
        # 🔥 统一模块初始化
        from core.trading_rules_preloader import get_trading_rules_preloader
        self.rules_preloader = get_trading_rules_preloader()

        # 状态管理
        self.current_status = ArbitrageStatus.IDLE
        self.current_session: Optional[ArbitrageSession] = None
        self.running = False
        # 🔥 修复：使用异步锁替代同步锁，避免"Lock is not acquired"错误
        self.lock = asyncio.Lock()
        self._main_loop_task = None  # 主循环任务

        # 性能统计
        self.total_sessions = 0
        self.successful_sessions = 0
        self.total_profit = 0.0

        # 配置参数 - 从.env中读取
        self.min_spread_percent = float(self.config.get("MIN_SPREAD", 0.001))  # 🔥 修复：最小价差0.1%，与.env保持一致
        self.max_spread_percent = float(self.config.get("MAX_SPREAD", 0.10))   # 最大价差10%
        # 🔥 修复：正确获取配置
        try:
            self.min_order_amount_usd = self.config.MIN_ORDER_AMOUNT_USD
        except:
            self.min_order_amount_usd = 90.0  # 应急默认值
        # 🔥 修复：删除硬编码代币，使用通用代币系统
        from core.universal_token_system import get_universal_token_system
        token_system = get_universal_token_system()
        supported_symbols = token_system.get_supported_symbols()
        self.target_symbol = supported_symbols[0] if supported_symbols else "ADA-USDT"  # 使用第一个支持的代币
        self.cooldown_period = float(self.config.get("ARBITRAGE_COOLDOWN", 30))  # 冷却期30秒

        # 交易组合配置 - 🔥 统一使用ABCDEF标识
        self.arbitrage_combinations = [
            ("gate", "spot", "bybit", "futures"),      # A: Gate现货 vs Bybit期货
            ("bybit", "spot", "gate", "futures"),      # B: Bybit现货 vs Gate期货
            ("okx", "spot", "bybit", "futures"),       # C: OKX现货 vs Bybit期货
            ("bybit", "spot", "okx", "futures"),       # D: Bybit现货 vs OKX期货
            ("okx", "spot", "gate", "futures"),        # E: OKX现货 vs Gate期货
            ("gate", "spot", "okx", "futures"),        # F: Gate现货 vs OKX期货
        ]

        # 执行状态
        self.is_executing = False
        self.last_arbitrage_time = 0

        # 🔥 余额缓存系统 - 按照全流程工作流.md
        self.balance_cache = {}
        
        # 🔥 新增：WebSocket回调系统 - 支持立即触发机制
        self.websocket_manager = None
        self._spread_callback_registered = False

        self.logger.info(f"套利引擎初始化: 最小订单金额=${self.min_order_amount_usd} {self.target_symbol}")
        self.logger.info(f"价差阈值: {self.min_spread_percent*100:.2f}% - {self.max_spread_percent*100:.2f}%")
        self.logger.info(f"支持组合: {len(self.arbitrage_combinations)}个")

        # 断言验证
        assert 0 < self.min_spread_percent < self.max_spread_percent, "Invalid spread configuration"

    async def start(self):
        """启动套利引擎 - 资金优化版本（非阻塞）"""
        try:
            self.logger.info("Starting ArbitrageEngine...")

            # 初始化各组件
            await self._initialize_components()

            # 🔥 新增：启动时一次性资金优化
            await self._startup_fund_optimization()

            # 🔥 新增：初始化余额缓存
            await self._initialize_balance_cache()

            # 🔥 修复：验证初始资金状态，但不因为资金不足而阻止启动
            try:
                await self._verify_initial_funds()
            except Exception as e:
                self.logger.warning(f"⚠️ 资金验证失败: {e}")
                self.logger.info("💡 ArbitrageEngine将继续启动，但可能无法执行套利直到资金充足")

            self.running = True
            self.current_status = ArbitrageStatus.SCANNING

            # 🔥 修复：启动主循环作为后台任务，不阻塞
            self._main_loop_task = asyncio.create_task(self._main_loop())

            self.logger.info("✅ ArbitrageEngine启动完成，主循环已在后台运行")

        except Exception as e:
            self.logger.error(f"Failed to start ArbitrageEngine: {e}")
            await self.stop()
            raise

    async def stop(self):
        """停止套利引擎 - 资金恢复版本"""
        self.logger.info("Stopping ArbitrageEngine...")

        self.running = False

        # 🔥 修复：停止主循环任务
        if hasattr(self, '_main_loop_task') and self._main_loop_task:
            self._main_loop_task.cancel()
            try:
                await self._main_loop_task
            except asyncio.CancelledError:
                self.logger.info("主循环任务已取消")

        # 🔥 新增：关闭时资金恢复
        await self._shutdown_fund_restoration()

        # 如果有正在进行的套利，尝试优雅关闭
        if self.current_session and self.current_session.status != ArbitrageStatus.COMPLETED:
            self.logger.warning("Force closing current arbitrage session")
            await self._force_close_session()

        # 停止各组件
        await self._cleanup_components()

        self.current_status = ArbitrageStatus.IDLE
        self.logger.info("ArbitrageEngine stopped")

    async def _shutdown_fund_restoration(self):
        """关闭时资金恢复"""
        try:
            self.logger.info("💰 开始关闭时资金恢复...")

            if self.fund_manager is None:
                self.logger.warning("资金管理器未初始化，跳过资金恢复")
                return

            # 恢复初始资金分配
            await self.fund_manager.restore_initial_balance()

            self.logger.info("✅ 关闭时资金恢复完成")

        except Exception as e:
            self.logger.error(f"关闭时资金恢复失败: {e}")

    async def _initialize_timestamp_sync(self):
        """🔥 **修复：使用统一模块**：调用统一时间戳同步系统，避免重复造轮子"""
        try:
            from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
            
            self.logger.info("🕐 开始调用统一时间戳同步系统...")
            
            # 🔥 使用统一模块，避免重复实现
            sync_results = await initialize_all_timestamp_processors(force_sync=True)
            
            # 分析同步结果
            sync_success_count = sum(1 for success in sync_results.values() if success)
            total_count = len(sync_results)
            
            # 记录同步结果
            for exchange, success in sync_results.items():
                if success:
                    self.logger.info(f"✅ {exchange.upper()} 时间戳同步成功")
                else:
                    self.logger.warning(f"⚠️ {exchange.upper()} 时间戳同步失败")
            
            # 验证同步结果
            if sync_success_count == total_count:
                self.logger.info(f"✅ 所有交易所时间戳同步成功 ({sync_success_count}/{total_count})")
                
                # 验证跨交易所时间戳一致性
                await self._validate_cross_exchange_timestamp_consistency()
                
            elif sync_success_count > 0:
                self.logger.warning(f"⚠️ 部分交易所时间戳同步成功 ({sync_success_count}/{total_count})")
                self.logger.warning("⚠️ 未同步的交易所可能会出现时间戳差异问题")
            else:
                self.logger.error("❌ 所有交易所时间戳同步失败")
                self.logger.error("❌ 系统将使用本地时间戳，可能出现大量时间戳不同步错误")
            
            # 设置时间戳监控任务
            self._setup_timestamp_monitoring()
            
            return sync_results
            
        except Exception as e:
            self.logger.error(f"❌ 时间戳同步系统初始化失败: {e}")
            self.logger.error("❌ 系统将继续启动，但可能出现时间戳同步问题")
            return {}

    async def _validate_cross_exchange_timestamp_consistency(self):
        """验证跨交易所时间戳一致性"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            exchanges = ["gate", "bybit", "okx"]
            current_time = time.time()
            
            # 获取所有交易所的时间戳
            timestamps = {}
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    timestamp = processor.get_synced_timestamp()
                    timestamps[exchange] = timestamp
                except Exception as e:
                    self.logger.warning(f"⚠️ 获取{exchange}时间戳失败: {e}")
                    continue
            
            if len(timestamps) < 2:
                self.logger.warning("⚠️ 无法验证跨交易所时间戳一致性，获取的时间戳不足")
                return
            
            # 计算时间戳差异
            timestamp_values = list(timestamps.values())
            max_diff = max(timestamp_values) - min(timestamp_values)
            
            # 阈值检查
            if max_diff <= 1000:  # 1000ms阈值
                self.logger.info(f"✅ 跨交易所时间戳一致性验证通过，最大差异: {max_diff:.1f}ms")
            else:
                self.logger.warning(f"⚠️ 跨交易所时间戳差异较大: {max_diff:.1f}ms > 1000ms")
                
                # 详细输出差异信息
                exchange_list = list(timestamps.keys())
                for i in range(len(exchange_list)):
                    for j in range(i+1, len(exchange_list)):
                        diff = abs(timestamps[exchange_list[i]] - timestamps[exchange_list[j]])
                        self.logger.warning(f"   {exchange_list[i]} vs {exchange_list[j]}: {diff:.1f}ms")
                        
        except Exception as e:
            self.logger.error(f"❌ 跨交易所时间戳一致性验证失败: {e}")

    def _setup_timestamp_monitoring(self):
        """设置时间戳同步状态监控"""
        try:
            # 每5分钟检查一次时间戳同步状态
            async def timestamp_monitor():
                while self.running:
                    try:
                        await asyncio.sleep(300)  # 5分钟间隔
                        await self._check_timestamp_sync_health()
                    except Exception as e:
                        self.logger.error(f"时间戳监控异常: {e}")
                        await asyncio.sleep(60)  # 错误后1分钟重试
            
            # 启动监控任务
            asyncio.create_task(timestamp_monitor())
            self.logger.info("✅ 时间戳同步监控任务已启动（5分钟间隔）")
            
        except Exception as e:
            self.logger.error(f"时间戳监控任务启动失败: {e}")

    async def _check_timestamp_sync_health(self):
        """检查时间戳同步健康状态"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            exchanges = ["gate", "bybit", "okx"]
            unsync_exchanges = []
            large_offset_exchanges = []
            
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    status = processor.get_sync_status()
                    
                    if not status.get("time_synced", False):
                        unsync_exchanges.append(exchange)
                    
                    offset = abs(status.get("time_offset_ms", 0))
                    if offset > 2000:  # 超过2秒认为异常
                        large_offset_exchanges.append(f"{exchange}({offset}ms)")
                        
                except Exception as e:
                    self.logger.warning(f"检查{exchange}时间戳状态失败: {e}")
                    unsync_exchanges.append(exchange)
            
            # 根据检查结果采取行动
            if unsync_exchanges:
                self.logger.warning(f"⚠️ 检测到未同步交易所: {', '.join(unsync_exchanges)}")
                self.logger.info("🔄 尝试使用统一模块重新同步...")
                # 🔥 使用统一模块进行重新同步，避免重复实现
                from websocket.unified_timestamp_processor import initialize_all_timestamp_processors
                await initialize_all_timestamp_processors(force_sync=True)
            
            if large_offset_exchanges:
                self.logger.warning(f"⚠️ 检测到时间偏移异常: {', '.join(large_offset_exchanges)}")
                
        except Exception as e:
            self.logger.error(f"时间戳健康检查失败: {e}")

    async def _initialize_components(self):
        """🔥 修复：删除重复初始化，使用已初始化的组件"""
        try:
            # 使用已传递的交易所实例和资金管理器
            if not hasattr(self, 'exchanges') or not self.exchanges:
                self.logger.error("交易所实例未传递给套利引擎")
                raise Exception("交易所实例未传递给套利引擎")

            # 🔥 **关键修复1**：系统启动时强制同步所有交易所时间戳处理器
            self.logger.info("🕐 初始化统一时间戳同步系统...")
            await self._initialize_timestamp_sync()

            # 🔥 **关键修复**：强制执行预加载，确保6大缓存系统完全激活
            self.logger.info("🚀 强制预加载所有交易规则...")
            preload_success = await self.rules_preloader.preload_all_trading_rules(self.exchanges)
            
            if not preload_success:
                self.logger.warning("⚠️ 交易规则预加载部分失败，但系统将继续运行")
            else:
                self.logger.info("✅ 交易规则预加载完成，6大缓存系统激活")
            
            # 验证预加载结果
            final_cache_stats = self.rules_preloader.get_cache_stats()
            total_rules = final_cache_stats.get('total_cache_entries', 0)
            self.logger.info(f"📊 预加载验证: {total_rules}条交易规则已缓存")

            if not hasattr(self, 'fund_manager') or not self.fund_manager:
                self.logger.warning("资金管理器未传递，将创建新实例")
                from fund_management.fund_manager import FundManager
                self.fund_manager = FundManager(self.exchanges)
                await self.fund_manager.initialize()

            # 延迟导入并初始化机会扫描器
            if self.opportunity_scanner is None:
                from core.opportunity_scanner import OpportunityScanner
                self.opportunity_scanner = OpportunityScanner()

            # 🔥 修复：OpportunityScanner初始化失败不应该阻止ArbitrageEngine启动
            try:
                await self.opportunity_scanner.initialize()
                self.logger.info("✅ OpportunityScanner初始化成功")
            except Exception as e:
                self.logger.warning(f"⚠️ OpportunityScanner初始化失败: {e}")
                self.logger.info("💡 ArbitrageEngine将继续启动，等待数据到达后激活扫描功能")

            # 🔥 删除重复初始化：使用已初始化的全局ExecutionEngine实例
            from core.execution_engine import get_execution_engine
            self.execution_engine = get_execution_engine()

            # 🔥 确保ExecutionEngine完全初始化
            if not self.execution_engine.exchanges:
                self.execution_engine.exchanges = self.exchanges

            # 🔥 强制完整初始化ExecutionEngine，确保order_manager存在
            await self.execution_engine.initialize()

            # 🔥 验证order_manager是否存在
            if not hasattr(self.execution_engine, 'order_manager') or self.execution_engine.order_manager is None:
                self.logger.error("❌ ExecutionEngine.order_manager未初始化")
                raise Exception("ExecutionEngine.order_manager未初始化")

            self.logger.info("✅ ExecutionEngine.order_manager验证通过")

            # 初始化监控器 - 🔥 修复：传入OpportunityScanner实例
            self.position_monitor = PositionMonitor(self.exchanges, self.opportunity_scanner)
            await self.position_monitor.initialize()

            self.risk_monitor = RiskMonitor(
                position_monitor=self.position_monitor,
                order_manager=self.execution_engine.order_manager,
                exchanges=self.exchanges
            )
            await self.risk_monitor.initialize()

            # 🔥 修复：初始化ConvergenceMonitor，确保在系统启动时就可用
            from core.convergence_monitor import init_convergence_monitor
            import os
            config = {
                "MAX_CONVERGENCE_WAIT": os.getenv("MAX_CONVERGENCE_WAIT", "1800"),
                "ENABLE_DYNAMIC_THRESHOLD": "true"  # 强制启用动态阈值
            }
            convergence_monitor = init_convergence_monitor(config, self.exchanges, self.opportunity_scanner)
            if convergence_monitor:
                self.logger.info("✅ ConvergenceMonitor初始化成功")
            else:
                self.logger.error("❌ ConvergenceMonitor初始化失败")

            # 🔥 新增：注册WebSocket回调，建立完整数据链路
            await self._register_websocket_callbacks()

            # 🔥 最终验证：确保缓存系统正常工作
            final_cache_stats = self.rules_preloader.get_cache_stats()
            self.logger.info(f"✅ 所有组件初始化完成，最终缓存状态：{final_cache_stats.get('total_cache_entries', 0)}条规则")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise

    async def _register_websocket_callbacks(self):
        """
        🔥 按照全流程工作流.md第2阶段要求：建立WebSocket → ArbitrageEngine完整链路
        注册WebSocket价差回调，实现立即触发机制
        """
        try:
            # 导入WebSocket管理器
            from websocket.ws_manager import WebSocketManager
            
            # 如果还没有WebSocket管理器实例，创建一个
            if self.websocket_manager is None:
                self.websocket_manager = WebSocketManager()
                
                # 🔥 **关键修复**：正确初始化WebSocket管理器
                self.logger.info("🚀 初始化WebSocket管理器...")
                
                # 1. 设置交易对
                from core.universal_token_system import get_universal_token_system
                token_system = get_universal_token_system()
                symbols = token_system.get_supported_symbols()
                self.websocket_manager.add_symbols(symbols)
                self.logger.info(f"✅ WebSocket设置交易对: {len(symbols)}个")
                
                # 2. 配置交易所
                exchanges_config = [
                    {"name": "gate", "spot": True, "futures": True},
                    {"name": "bybit", "spot": True, "futures": True},
                    {"name": "okx", "spot": True, "futures": True}
                ]
                
                # 3. 初始化客户端
                init_success = await self.websocket_manager.initialize_clients(exchanges_config)
                if not init_success:
                    self.logger.error("❌ WebSocket客户端初始化失败")
                    return
                
                # 4. 启动WebSocket连接
                await self.websocket_manager.start()
                
                # 🔥 **关键修复**：启动统一连接池管理器的数据流健康检查
                if hasattr(self.websocket_manager, 'connection_pool_manager') and self.websocket_manager.connection_pool_manager:
                    try:
                        # 启动数据流健康检查任务
                        import asyncio
                        health_check_task = asyncio.create_task(
                            self.websocket_manager.connection_pool_manager.start_data_flow_health_check()
                        )
                        self.logger.info("✅ 数据流健康检查已启动")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 数据流健康检查启动失败: {e}")
                
                self.logger.info("✅ WebSocket管理器启动成功")
            
            # 🔥 关键：注册价差检测回调
            if not self._spread_callback_registered:
                self.websocket_manager.register_callback("spread", self._on_websocket_spread_opportunity)
                self._spread_callback_registered = True
                self.logger.info("✅ WebSocket价差回调已注册，立即触发机制激活")
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket回调注册失败: {e}")
            # 不抛出异常，允许系统继续运行

    async def _on_websocket_spread_opportunity(self, spread_data: Dict[str, Any]):
        """
        🔥 按照全流程工作流.md第2阶段：立即触发机制
        WebSocket检测到价差机会时的回调处理
        """
        try:
            self.logger.debug(f"🔥 WebSocket触发价差检查: {spread_data.get('symbol')}")
            
            # 如果当前正在执行套利，跳过
            if self.is_executing:
                self.logger.debug("  ⏳ 当前正在执行套利，跳过此次机会")
                return
            
            # 🔥 立即触发机会扫描和验证
            symbol = spread_data.get('symbol')
            if symbol:
                # 异步扫描特定交易对的套利机会
                opportunities = await self.opportunity_scanner.scan_symbol_opportunities(symbol)
                
                for opportunity in opportunities:
                    # 🔥 零延迟验证（使用6大缓存+API系统）
                    if await self._validate_opportunity(opportunity):
                        self.logger.info(f"🎯 WebSocket触发套利机会: {opportunity.symbol} 价差{opportunity.spread_percent*100:.3f}%")
                        
                        # 立即开始套利会话
                        await self._start_arbitrage_session(opportunity)
                        break  # 一次只执行一个机会
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket价差回调处理异常: {e}")

    async def _verify_initial_funds(self):
        """验证初始资金状态"""
        try:
            # 检查并调整资金分配
            await self.fund_manager.check_and_adjust_balance()

            # 获取各交易所余额
            balances = await self.fund_manager.get_all_balances()

            self.logger.info("Initial fund status:")
            total_funds = 0.0
            for exchange, balance_data in balances.items():
                # 🔥 修复：根据返回的数据结构正确提取总余额，确保类型转换
                if 'total_usdt' in balance_data:
                    total_usdt = float(balance_data['total_usdt']) if balance_data['total_usdt'] is not None else 0.0
                elif 'unified' in balance_data:
                    unified_balance = balance_data['unified']
                    if isinstance(unified_balance, dict):
                        # 🔥 修复：正确解析统一账户格式
                        usdt_info = unified_balance.get('USDT', {"available": 0.0, "locked": 0.0})
                        if isinstance(usdt_info, dict):
                            total_usdt = float(usdt_info.get('available', 0.0)) + float(usdt_info.get('locked', 0.0))
                        else:
                            total_usdt = float(usdt_info) if usdt_info else 0.0
                    else:
                        total_usdt = 0.0
                else:
                    # 🔥 修复：处理分离账户格式
                    spot_usdt = balance_data.get('spot_usdt', 0.0)
                    futures_usdt = balance_data.get('futures_usdt', 0.0)
                    total_usdt = float(spot_usdt) + float(futures_usdt)
                
                # 确保total_usdt为数字类型
                if not isinstance(total_usdt, (int, float)):
                    self.logger.error(f"{exchange}余额格式错误: {type(total_usdt)}, 值: {total_usdt}")
                    total_usdt = 0.0
                
                total_funds += total_usdt
                self.logger.info(f"{exchange}: ${total_usdt:.2f} USDT")

            # 从配置文件获取资金要求设置
            allow_low_balance = self.config.get("ALLOW_LOW_BALANCE_TEST", "false").lower() == "true"
            min_required_balance = float(self.config.get("MIN_REQUIRED_BALANCE", "100.0"))
            min_capital_requirement = float(self.config.get("MIN_CAPITAL_REQUIREMENT", "500"))
            
            if allow_low_balance:
                if total_funds >= min_required_balance:
                    self.logger.info(f"✅ 低余额测试模式: 总资金 {format_currency(total_funds)} 满足最小要求 {format_currency(min_required_balance)}")
                else:
                    raise Exception(f"❌ 资金不足: {format_currency(total_funds)} < {format_currency(min_required_balance)}")
            else:
                if total_funds >= min_capital_requirement:
                    self.logger.info(f"✅ 总资金: {format_currency(total_funds)} 满足要求 (>= {format_currency(min_capital_requirement)})")
                else:
                    raise Exception(f"❌ 资金不足: {format_currency(total_funds)} < {format_currency(min_capital_requirement)}")

        except Exception as e:
            self.logger.error(f"Fund verification failed: {e}")
            raise

    async def _startup_fund_optimization(self):
        """启动时一次性资金优化"""
        try:
            self.logger.info("💰 开始启动时资金优化...")

            if self.fund_manager is None:
                self.logger.warning("资金管理器未初始化，跳过资金优化")
                return

            # 执行一次性资金调整
            await self.fund_manager.check_and_adjust_balance()

            # Gate.io自动划转到50%-50%
            if 'gate' in self.exchanges:
                await self._balance_gate_funds()

            self.logger.info("✅ 启动时资金优化完成")

        except Exception as e:
            self.logger.error(f"启动时资金优化失败: {e}")

    async def _balance_gate_funds(self):
        """平衡Gate.io资金分配"""
        try:
            gate_exchange = self.exchanges.get('gate')
            if not gate_exchange:
                return

            # 获取总余额
            total_balance = await gate_exchange.get_total_balance()
            usdt_total = total_balance.get('USDT', 0)

            if usdt_total > 100:  # 只有余额超过100才调整
                target_spot = usdt_total * 0.5
                target_futures = usdt_total * 0.5

                # 执行资金划转
                await self.fund_manager.transfer_service.transfer_to_target_allocation(
                    'gate', {'spot': target_spot, 'futures': target_futures}
                )

                self.logger.info(f"Gate.io资金平衡完成: 现货${target_spot:.2f}, 期货${target_futures:.2f}")

        except Exception as e:
            self.logger.error(f"Gate.io资金平衡失败: {e}")

    async def _initialize_balance_cache(self):
        """🔥 强制使用UnifiedBalanceManager - 删除重复余额缓存逻辑"""
        try:
            self.logger.info("💰 初始化余额缓存...")
            
            # 🔥 强制使用统一余额管理器，删除所有重复逻辑
            from core.unified_balance_manager import get_unified_balance_manager
            balance_manager = get_unified_balance_manager(self.exchanges)
            
            # 保存旧的余额缓存用于对比
            old_balance_cache = self.balance_cache.copy()

            # 使用统一管理器更新余额缓存
            self.balance_cache = await balance_manager.get_all_balances()

            # 🔥 记录余额缓存更新
            for key, new_balance in self.balance_cache.items():
                old_balance = old_balance_cache.get(key, 0.0)
                if old_balance != new_balance:
                    log_balance_update("system", key, old_balance, new_balance)

            self.logger.info("✅ [统一模块] 余额缓存: UnifiedBalanceManager | 删除重复逻辑，强制使用统一模块")
            self.logger.info(f"   缓存余额数: {len(self.balance_cache)}个账户")

            for key, balance in self.balance_cache.items():
                self.logger.info(f"   {key}: ${balance:.2f}")
                
        except Exception as e:
            self.logger.error(f"❌ 统一余额管理器调用失败: {e}")
            self.balance_cache = {}

    async def update_balance_cache(self):
        """🔥 强制使用UnifiedBalanceManager - 删除重复更新逻辑"""
        # 直接调用初始化方法，内部使用统一管理器
        await self._initialize_balance_cache()

    async def _main_loop(self):
        """主循环"""
        self.logger.info("Starting main arbitrage loop")

        while self.running:
            try:
                # 状态机处理
                if self.current_status == ArbitrageStatus.SCANNING:
                    await self._handle_scanning()
                elif self.current_status == ArbitrageStatus.PREPARING:
                    await self._handle_preparing()
                elif self.current_status == ArbitrageStatus.EXECUTING:
                    await self._handle_executing()
                elif self.current_status == ArbitrageStatus.WAITING_CONVERGENCE:
                    await self._handle_waiting_convergence()
                elif self.current_status == ArbitrageStatus.CLOSING:
                    await self._handle_closing()
                elif self.current_status == ArbitrageStatus.COMPLETED:
                    await self._handle_completed()
                elif self.current_status == ArbitrageStatus.ERROR:
                    await self._handle_error()

                # 🎯 性能优化：使用0.1秒间隔，平衡性能和精准度
                # 1ms过度优化导致CPU占用过高，0.1秒足够捕获套利机会
                await asyncio.sleep(0.1)

            except KeyboardInterrupt:
                self.logger.info("Interrupted by user")
                await self.stop()
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                self.current_status = ArbitrageStatus.ERROR
                await asyncio.sleep(1)

    async def _handle_scanning(self):
        """处理扫描阶段 - 🔥 修复：增强错误处理和状态检查"""
        try:
            # 🔥 修复：检查OpportunityScanner是否可用
            if not self.opportunity_scanner or not getattr(self.opportunity_scanner, 'running', False):
                # OpportunityScanner未就绪，等待更长时间
                await asyncio.sleep(0.1)  # 100ms等待
                return

            # 扫描套利机会
            opportunities = await self.opportunity_scanner.scan_opportunities()

            if opportunities:
                best_opportunity = max(opportunities, key=lambda x: x.spread_percent)

                # 验证机会是否符合条件
                if await self._validate_opportunity(best_opportunity):
                    await self._start_arbitrage_session(best_opportunity)

            # 🎯 优化扫描延迟：使用0.1秒间隔，避免CPU过载
            await asyncio.sleep(0.1)  # 100ms扫描间隔，平衡性能和响应速度

        except Exception as e:
            self.logger.error(f"Error in scanning: {e}")
            await asyncio.sleep(1)

    async def _handle_preparing(self):
        """处理准备阶段"""
        try:
            if not self.current_session:
                self.current_status = ArbitrageStatus.SCANNING
                return

            # 资金准备
            await self._prepare_funds()

            # 切换到执行阶段
            self.current_status = ArbitrageStatus.EXECUTING
            self.logger.info(f"Session {self.current_session.session_id} prepared, starting execution")

        except Exception as e:
            self.logger.error(f"Error in preparing: {e}")
            self.current_status = ArbitrageStatus.ERROR

    async def _handle_executing(self):
        """处理执行阶段"""
        try:
            if not self.current_session:
                self.current_status = ArbitrageStatus.SCANNING
                return

            # 执行套利交易
            success = await self.execution_engine.execute_arbitrage(
                self.current_session.opportunity
            )

            if success:
                # 获取成交信息
                execution_result = await self.execution_engine.get_execution_result()
                self._update_session_from_execution(execution_result)

                # 切换到等待收敛阶段
                self.current_status = ArbitrageStatus.WAITING_CONVERGENCE
                self.logger.info(f"Session {self.current_session.session_id} executed, waiting for convergence")
            else:
                self.logger.error(f"Execution failed for session {self.current_session.session_id}")
                self.current_status = ArbitrageStatus.ERROR

        except Exception as e:
            self.logger.error(f"Error in executing: {e}")
            self.current_status = ArbitrageStatus.ERROR

    async def _handle_waiting_convergence(self):
        """处理等待趋同阶段 - 🔥 修复：添加状态检查，防止重复通知"""
        try:
            if not self.current_session:
                self.current_status = ArbitrageStatus.SCANNING
                return

            opportunity = self.current_session.opportunity
            
            # 🔥 核心修复：检查ExecutionEngine的执行结果，确认是否有实际交易
            execution_result = await self.execution_engine.get_execution_result()

            # 检查是否真正执行了开仓操作
            if not execution_result or not execution_result.success:
                self.logger.info(f"⚠️ 无有效交易执行结果，跳过趋同监控: {opportunity.symbol}")
                self.current_status = ArbitrageStatus.COMPLETED
                return

            # 进一步检查是否有实际成交的订单
            has_actual_trades = False
            if execution_result:
                # 🔥 修复：使用getattr避免AttributeError
                spot_result = getattr(execution_result, 'spot_result', None)
                futures_result = getattr(execution_result, 'futures_result', None)

                spot_success = spot_result and getattr(spot_result, 'success', False)
                futures_success = futures_result and getattr(futures_result, 'success', False)

                if spot_success and futures_success:
                    has_actual_trades = True
                    self.logger.info(f"✅ 确认有实际交易，继续趋同监控: {opportunity.symbol}")
                else:
                    self.logger.info(f"❌ 无实际成交订单: 现货{'成功' if spot_success else '失败'}, 期货{'成功' if futures_success else '失败'}")
            else:
                self.logger.info(f"❌ 执行结果数据不完整，跳过趋同监控")
            
            # 🔥 只有实际有交易时才进入趋同监控
            if not has_actual_trades:
                self.logger.info(f"📝 无实际交易订单，直接完成套利会话: {opportunity.symbol}")
                self.current_status = ArbitrageStatus.COMPLETED
                return

            # 从.env获取配置参数 - 🎯 优化趋同检查频率
            import os
            convergence_check_interval = float(os.getenv("CONVERGENCE_CHECK_INTERVAL", "0.5"))
            max_convergence_wait = float(os.getenv("MAX_CONVERGENCE_WAIT", "1800"))
            # 🔥 **架构优化**: 移除固定阈值，使用动态阈值系统

            session_duration = time.time() - self.current_session.start_time
            # 🔥 修复：使用ConvergenceMonitor统一接口获取当前价差，避免重复实现
            from core.convergence_monitor import get_convergence_monitor
            convergence_monitor = get_convergence_monitor()

            if not convergence_monitor:
                self.logger.error("ConvergenceMonitor未初始化")
                current_spread = None
            else:
                current_spread = await convergence_monitor.get_current_spread(opportunity.symbol)

            current_spread_percent = current_spread * 100 if current_spread else 0.0

            should_close = False
            close_reason = ""

            # 🔥 **架构优化**: 使用ConvergenceMonitor的统一动态阈值检测逻辑
            if convergence_monitor and await convergence_monitor.detect_convergence_signal(opportunity.symbol):
                should_close = True
                close_reason = f"动态阈值判断应该平仓: {current_spread_percent:.3f}%"
            elif session_duration >= max_convergence_wait:
                should_close = True
                close_reason = f"超过最大等待时间: {session_duration:.1f}s >= {max_convergence_wait}s"

            # 🔥 **架构优化**: 记录详细的趋同监控日志
            self.logger.info(f"🔍 价差趋同监控: {opportunity.symbol}")
            self.logger.info(f"   当前价差: {current_spread_percent:.3f}%")
            self.logger.info(f"   平仓阈值: 使用动态阈值系统")
            self.logger.info(f"   持有时间: {session_duration:.1f}s / {max_convergence_wait}s")
            self.logger.info(f"   趋同状态: {'✅准备平仓' if should_close else '⏳继续等待'}")

            if should_close:
                self.current_status = ArbitrageStatus.CLOSING
                self.logger.info(f"🎯 触发平仓条件: {close_reason}")

                # 🔥 修复：价差趋同是正常套利流程，不是异常！
                # 发送套利进度通知，而不是错误通知
                from utils.notification import send_notification, NotificationLevel
                await send_notification(
                    message=f"🎯 套利趋同通知\n💱 交易对: {opportunity.symbol}\n🏢 交易所: {opportunity.buy_exchange}+{opportunity.sell_exchange}\n📊 趋同原因: {close_reason}\n💰 交易量: {opportunity.base_amount:.6f}\n⏰ 开始平仓",
                    level=NotificationLevel.INFO  # ✅ 修复：使用INFO级别，不是ERROR
                )
            else:
                # 继续等待，使用配置的检查间隔
                await asyncio.sleep(convergence_check_interval)

        except Exception as e:
            self.logger.error(f"Error in waiting convergence: {e}")
            self.current_status = ArbitrageStatus.ERROR

    # 🔥 删除重复方法：_get_current_spread()
    # 统一使用 ConvergenceMonitor.get_current_spread() 避免重复实现
    # 所有价差获取都通过ConvergenceMonitor → OpportunityScanner的统一链路

    async def _handle_closing(self):
        """处理平仓阶段 - 🔥 修复：检查是否实际需要平仓"""
        try:
            if not self.current_session:
                self.current_status = ArbitrageStatus.SCANNING
                return

            # 🔥 关键修复：检查ExecutionEngine状态，只有实际执行了交易才需要平仓
            execution_result = await self.execution_engine.get_execution_result()
            
            # 检查是否有实际的交易订单
            has_actual_trades = False
            if execution_result:
                # 🔥 修复：添加兼容性处理，使用getattr避免AttributeError
                spot_orders = getattr(execution_result, 'spot_orders', [])
                futures_orders = getattr(execution_result, 'futures_orders', [])

                if spot_orders or futures_orders:
                    # 进一步检查是否有实际成交的订单
                    filled_spot_orders = [order for order in spot_orders if getattr(order, 'filled', 0) > 0]
                    filled_futures_orders = [order for order in futures_orders if getattr(order, 'filled', 0) > 0]

                    if filled_spot_orders or filled_futures_orders:
                        has_actual_trades = True
                        self.logger.info(f"检测到实际交易: 现货订单{len(filled_spot_orders)}个, 期货订单{len(filled_futures_orders)}个")
                    else:
                        self.logger.info("无实际成交的订单，跳过平仓流程")
                else:
                    self.logger.info("无交易订单，跳过平仓流程")
            else:
                self.logger.info("无执行结果，跳过平仓流程")
            
            if has_actual_trades:
                # 🔥 只有实际有交易时才执行平仓
                self.logger.info("执行平仓操作...")
                success = await self.execution_engine.close_positions(
                    self.current_session.opportunity
                )

                if success:
                    # 计算收益
                    await self._calculate_session_profit()

                    # 恢复资金平衡
                    await self.fund_manager.restore_balance()

                    self.current_status = ArbitrageStatus.COMPLETED
                    self.logger.info(f"Session {self.current_session.session_id} closed successfully")
                else:
                    self.logger.error(f"Failed to close session {self.current_session.session_id}")
                    # 🔥 修复：平仓失败时不应该停止系统，而是继续监控
                    self.logger.warning(f"⚠️ 平仓失败，继续监控价差变化，等待下次平仓机会")

                    # 🔥 新增：恢复ConvergenceMonitor监控
                    try:
                        from core.convergence_monitor import get_convergence_monitor
                        monitor = get_convergence_monitor()
                        if monitor and self.current_session:
                            opportunity = self.current_session.opportunity
                            await monitor.resume_monitoring(opportunity.symbol)
                            self.logger.info(f"✅ 已恢复价差监控: {opportunity.symbol}")

                            # 🔥 关键修复：平仓失败后不释放并行控制槽位，继续占用直到成功平仓
                            self.logger.info(f"🔒 保持并行控制槽位占用: {opportunity.symbol} (等待平仓成功)")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 恢复监控失败: {e}")

                    # 🔥 关键修复：平仓失败后返回等待收敛状态，而不是ERROR状态
                    self.current_status = ArbitrageStatus.WAITING_CONVERGENCE
            else:
                # 🔥 无实际交易时，直接标记为完成
                self.logger.info("无实际交易，直接完成会话")
                self.current_status = ArbitrageStatus.COMPLETED
                
                # 🔥 修复：设置会话结束时间
                if self.current_session:
                    self.current_session.end_time = time.time()
                    self.current_session.status = ArbitrageStatus.COMPLETED

        except Exception as e:
            self.logger.error(f"Error in closing: {e}")
            self.current_status = ArbitrageStatus.ERROR

    async def _handle_completed(self):
        """处理完成阶段 - 🔥 修复：统一状态管理和冷却期"""
        try:
            if self.current_session:
                # 🔥 新增：套利结束后余额查询和确认
                await self._post_arbitrage_verification()

                # 完成会话
                self._finalize_session()

            # 🔥 修复：统一状态重置 - 确保状态同步
            self._reset_execution_state()

            # 🔥 修复：统一冷却期管理 - 只使用.env配置
            self.logger.info(f"📝 套利完成，等待冷却期: {self.cooldown_period}秒")
            await asyncio.sleep(self.cooldown_period)
            self.logger.info(f"✅ 冷却期结束，返回扫描状态")

            # 返回扫描状态
            self.current_status = ArbitrageStatus.SCANNING

        except Exception as e:
            self.logger.error(f"Error in completed: {e}")
            # 🔥 修复：错误时也要重置状态
            self._reset_execution_state()
            self.current_status = ArbitrageStatus.ERROR
            
    async def _post_arbitrage_verification(self):
        """套利结束后验证 - 余额查询和确认套利成功"""
        try:
            if not self.current_session:
                return
                
            opportunity = self.current_session.opportunity
            self.logger.info("=" * 80)
            self.logger.info(f"🔍 【套利后验证】 {opportunity.symbol}")
            self.logger.info("=" * 80)
            
            # 1. 查询最新余额
            self.logger.info("📊 1. 查询套利后余额...")
            await self.update_balance_cache()
            
            # 2. 检查持仓是否清零
            self.logger.info("📊 2. 检查持仓是否清零...")
            await self._verify_positions_cleared(opportunity)
            
            # 3. 确认套利成功
            self.logger.info("📊 3. 确认套利成功...")
            if self.current_session.realized_profit > 0:
                self.logger.info(f"✅ 套利成功确认: 利润 ${self.current_session.realized_profit:.4f}")
            else:
                self.logger.warning(f"⚠️ 套利利润为零或负数: ${self.current_session.realized_profit:.4f}")
                
            self.logger.info("=" * 80)
            self.logger.info(f"✅ 【套利后验证完成】 {opportunity.symbol}")
            self.logger.info("=" * 80)
            
        except Exception as e:
            self.logger.error(f"套利后验证失败: {e}")
            
    async def _verify_positions_cleared(self, opportunity: ArbitrageOpportunity):
        """验证持仓是否清零 - 🔥 修复：使用UnifiedBalanceManager替代直接API调用"""
        try:
            # 🔥 使用统一余额管理器替代直接API调用
            from core.unified_balance_manager import get_unified_balance_manager
            from exchanges.exchanges_base import AccountType

            balance_manager = get_unified_balance_manager(self.exchanges)
            spot_exchange = self.exchanges.get(opportunity.buy_exchange)
            futures_exchange = self.exchanges.get(opportunity.sell_exchange)
            base_currency = opportunity.symbol.split('-')[0]

            # 检查现货余额 - 使用统一管理器
            if spot_exchange:
                try:
                    # 🔥 修复：使用UnifiedBalanceManager.get_balance_unified()
                    balance = await balance_manager.get_balance_unified(spot_exchange, AccountType.SPOT)
                    spot_balance = balance.get(base_currency, {}).get('available', 0)
                    self.logger.info(f"   现货持仓({opportunity.buy_exchange}): {spot_balance:.6f} {base_currency}")
                except Exception as e:
                    self.logger.warning(f"   现货持仓查询失败: {e}")

            # 检查期货持仓 - 使用统一管理器
            if futures_exchange:
                try:
                    # 🔥 修复：使用UnifiedBalanceManager.get_position_unified()
                    positions = await balance_manager.get_position_unified(futures_exchange, opportunity.symbol)
                    futures_position = 0
                    for pos in positions:
                        if pos.get('symbol') == opportunity.symbol:
                            futures_position = abs(float(pos.get('size', 0)))
                            break
                    self.logger.info(f"   期货持仓({opportunity.sell_exchange}): {futures_position:.6f} {base_currency}")
                except Exception as e:
                    self.logger.warning(f"   期货持仓查询失败: {e}")

        except Exception as e:
            self.logger.error(f"持仓验证失败: {e}")

    async def _handle_error(self):
        """处理错误阶段 - 🔥 增强错误恢复机制"""
        try:
            # 🚨 修复：减少错误日志频率，避免刷屏
            if not hasattr(self, '_last_error_log_time'):
                self._last_error_log_time = {}

            current_time = time.time()
            session_id = self.current_session.session_id if self.current_session else "unknown"

            # 每个session只记录一次错误，避免重复日志
            if session_id not in self._last_error_log_time:
                self.logger.error(f"🔧 处理错误状态: session {session_id}")
                self._last_error_log_time[session_id] = current_time

            # 🔥 修复：使用统一的SystemMonitor进行错误分析和恢复策略
            from core.system_monitor import get_system_monitor
            system_monitor = get_system_monitor()

            # 委托给SystemMonitor进行错误分析和恢复策略制定
            recovery_strategy = await system_monitor.analyze_error_and_get_recovery_strategy(
                arbitrage_engine=self,
                execution_engine=self.execution_engine
            )

            recovery_cooldown = recovery_strategy.get('cooldown', 10)
            self.logger.info(f"🔍 错误恢复策略: {recovery_strategy.get('description', '默认策略')}")

            # 🔥 增强：清理session并记录详细信息
            if self.current_session:
                self.logger.info(f"🧹 清理错误会话: {self.current_session.session_id}")
                self.logger.info(f"   交易对: {self.current_session.opportunity.symbol}")
                self.logger.info(f"   交易所: {self.current_session.opportunity.buy_exchange} + {self.current_session.opportunity.sell_exchange}")

                # 标记会话为错误状态
                self.current_session.status = ArbitrageStatus.ERROR
                self.current_session.end_time = time.time()
                self.current_session = None

            # 🔥 修复：强制状态重置，确保系统不卡住
            self._reset_execution_state(force_full_reset=True)

            # 🔥 修复：使用统一的SystemMonitor进行健康检查和恢复验证
            health_ok = await system_monitor.perform_health_check_for_component('arbitrage_engine', self)
            if not health_ok:
                self.logger.warning("⚠️ 系统健康检查失败，延长恢复时间")
                recovery_cooldown *= 2

            # 🔥 修复：智能冷却期管理
            self.logger.info(f"⏰ 错误恢复冷却期: {recovery_cooldown}秒")
            await asyncio.sleep(recovery_cooldown)

            # 🔥 修复：使用统一的SystemMonitor进行恢复验证
            if await system_monitor.verify_system_ready_for_recovery():
                self.logger.info("✅ 系统恢复完成，返回扫描状态")
                self.current_status = ArbitrageStatus.SCANNING
            else:
                self.logger.warning("⚠️ 系统恢复验证失败，延长冷却期")
                await asyncio.sleep(10)
                self.current_status = ArbitrageStatus.SCANNING

        except Exception as e:
            # 🔥 修复：即使错误处理失败，也要确保返回扫描状态
            self.logger.error(f"❌ 错误处理异常: {e}")
            self.current_session = None  # 强制清除session
            self._reset_execution_state(force_full_reset=True)  # 强制完整重置
            self.current_status = ArbitrageStatus.SCANNING
            # 🔥 修复：严重错误时使用更长但合理的冷却期
            await asyncio.sleep(max(self.cooldown_period * 3, 15))  # 最多3倍冷却期，最少15秒

    def _reset_execution_state(self, force_full_reset: bool = False):
        """🔥 统一状态重置方法 - 确保is_executing和current_status同步

        Args:
            force_full_reset: 是否执行完整的强制重置（包括清理监控、锁等）
        """
        try:
            if force_full_reset:
                self.logger.warning("🔧 开始强制重置所有状态...")

                # 1. 重置基本执行状态
                self.is_executing = False
                self.current_status = ArbitrageStatus.SCANNING
                self.current_session = None

                # 2. 清理ConvergenceMonitor
                try:
                    from core.convergence_monitor import get_convergence_monitor
                    monitor = get_convergence_monitor()
                    if monitor and hasattr(monitor, 'active_monitors'):
                        active_symbols = list(monitor.active_monitors.keys())
                        for symbol in active_symbols:
                            try:
                                monitor.stop_monitoring(symbol)
                                self.logger.info(f"✅ 停止监控: {symbol}")
                            except Exception as e:
                                self.logger.warning(f"⚠️ 停止监控失败 {symbol}: {e}")
                        monitor.active_monitors.clear()
                except Exception as e:
                    self.logger.warning(f"⚠️ 清理ConvergenceMonitor失败: {e}")

                # 3. 释放所有锁 - 修复RLock没有locked()方法的问题
                if hasattr(self, 'lock'):
                    try:
                        # 🔥 修复：异步锁的正确释放方式
                        if self.lock.locked():
                            self.lock.release()
                            self.logger.info("✅ 全局锁已释放")
                        else:
                            self.logger.debug("🔧 全局锁未被持有，无需释放")
                    except Exception as e:
                        # 锁可能没有被持有，这是正常的
                        self.logger.debug(f"🔧 锁释放信息: {e}")

                # 4. 清理ExecutionEngine状态
                try:
                    from core.execution_engine import get_execution_engine
                    exec_engine = get_execution_engine()
                    if exec_engine:
                        exec_engine.current_execution = None
                        # ExecutionEngine使用asyncio.Lock，不能在同步方法中释放
                        # 这里只是清理状态，不释放异步锁
                        self.logger.debug("🔧 ExecutionEngine状态已清理")
                except Exception as e:
                    self.logger.warning(f"⚠️ 清理ExecutionEngine失败: {e}")

                self.logger.warning("✅ 强制重置所有状态完成")
            else:
                # 基本状态重置
                self.is_executing = False
                self.logger.debug("✅ 执行状态已重置: is_executing = False")

        except Exception as e:
            self.logger.error(f"Error resetting execution state: {e}")

    # 🔥 修复：删除重复方法，统一使用SystemMonitor
    # 原有的 _analyze_error_type, _perform_health_check, _verify_system_ready 方法
    # 已移动到 SystemMonitor 模块，避免职责重复

    async def monitor_websocket_health(self):
        """🔥 重构：使用SystemMonitor统一监控WebSocket连接健康状态"""
        try:
            # 🔥 重构：委托给SystemMonitor，避免重复实现
            from core.system_monitor import get_system_monitor, HealthStatus
            system_monitor = get_system_monitor()

            # 检查WebSocket连接状态
            ws_health = await system_monitor._check_websocket_connections()

            if ws_health['status'] == HealthStatus.FAILED:
                self.logger.warning("⚠️ WebSocket连接健康检查失败")
                # 尝试触发系统恢复
                recovery_success = await system_monitor.trigger_recovery()
                return recovery_success
            elif ws_health['status'] == HealthStatus.WARNING:
                self.logger.warning(f"⚠️ WebSocket连接状态警告: {ws_health.get('issues', [])}")
                return True  # 警告状态仍可继续运行
            else:
                self.logger.debug(f"✅ WebSocket连接健康: {ws_health.get('details', {})}")
                return True
        except Exception as e:
            self.logger.error(f"❌ WebSocket健康检查失败: {e}")
            return False

    async def _validate_opportunity(self, opportunity: ArbitrageOpportunity) -> bool:
        """验证套利机会 - 期货溢价套利专用"""
        try:
            # 期货溢价套利：只有期货价格 > 现货价格才是机会 (正价差)
            spread_percent = opportunity.spread_percent
            
            self.logger.debug(f"验证期货溢价机会: {opportunity.symbol}")
            self.logger.debug(f"  价差: {spread_percent*100:.3f}% ({'期货溢价' if spread_percent > 0 else '现货溢价'})")
            self.logger.debug(f"  最小溢价阈值: {self.min_spread_percent*100:.3f}%")
            
            # 只有正价差且超过最小阈值才是有效的期货溢价机会
            if spread_percent <= 0:
                self.logger.debug(f"  ❌ 非期货溢价: {spread_percent*100:.3f}% <= 0 (期货价格不高于现货)")
                return False
                
            if spread_percent < self.min_spread_percent:
                self.logger.debug(f"  ❌ 期货溢价不足: {spread_percent*100:.3f}% < {self.min_spread_percent*100:.3f}%")
                return False
                
            if spread_percent > self.max_spread_percent:
                self.logger.debug(f"  ❌ 期货溢价过高: {spread_percent*100:.3f}% > {self.max_spread_percent*100:.3f}%")
                return False
            
            self.logger.debug(f"  ✅ 期货溢价满足条件: {spread_percent*100:.3f}%")

            # 检查风险
            risk_alerts = await self.risk_monitor.check_spread_risk(
                opportunity.symbol,
                opportunity.exchange1_price,
                opportunity.exchange2_price
            )
            
            # 如果有高风险或严重风险告警，拒绝机会
            high_risk_alerts = [alert for alert in risk_alerts if alert.risk_level.value in ['high', 'critical']]
            if high_risk_alerts:
                self.logger.debug(f"  ❌ 风险验证失败: 发现{len(high_risk_alerts)}个高风险告警")
                for alert in high_risk_alerts:
                    self.logger.debug(f"    - {alert.title}: {alert.description}")
                return False
            
            self.logger.debug(f"  ✅ 风险验证通过")

            # 🔥 按照全流程工作流.md第2阶段：零延迟验证（使用统一缓存）
            # 🚀 余额检查（0.00ms）：使用ArbitrageEngine余额缓存
            if not self._validate_balance_from_cache(opportunity):
                self.logger.debug(f"  ❌ 余额缓存检查失败")
                return False

            self.logger.debug(f"  ✅ 余额缓存检查通过")

            # 🚀 保证金检查（0.00ms）：使用MarginCalculator保证金缓存
            if not await self._validate_margin_from_cache(opportunity):
                self.logger.debug(f"  ❌ 保证金缓存检查失败")
                return False

            self.logger.debug(f"  ✅ 保证金缓存检查通过")

            # 🚀 精度数据（0.00ms）：使用TradingRulesPreloader精度缓存
            if not self._validate_precision_from_cache(opportunity):
                self.logger.debug(f"  ❌ 精度数据缓存检查失败")
                return False

            self.logger.debug(f"  ✅ 精度数据缓存检查通过")

            # 🚀 交易规则（0.00ms）：使用TradingRulesPreloader交易规则缓存
            if not self._validate_rules_from_cache(opportunity):
                self.logger.debug(f"  ❌ 交易规则缓存检查失败")
                return False

            self.logger.debug(f"  ✅ 交易规则缓存检查通过")

            # 检查深度充足性
            if not self._validate_depth(opportunity):
                self.logger.debug(f"  ❌ 深度不足")
                return False

            self.logger.debug(f"  ✅ 深度充足")
            self.logger.info(f"✅ 期货溢价机会验证通过: {opportunity.symbol} 溢价{spread_percent*100:.3f}%")

            return True

        except Exception as e:
            self.logger.error(f"Error validating opportunity: {e}")
            return False

    def _validate_balance_from_cache(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 按照全流程工作流.md第2阶段：零延迟验证
        🚀 余额检查（0.00ms）：使用ArbitrageEngine余额缓存
        """
        try:
            # 获取交易所和数量信息
            spot_exchange = opportunity.buy_exchange
            futures_exchange = opportunity.sell_exchange
            symbol = opportunity.symbol
            base_amount = opportunity.base_amount

            # 获取价格
            spot_price = opportunity.exchange1_price if opportunity.exchange1_market == 'spot' else opportunity.exchange2_price
            futures_price = opportunity.exchange1_price if opportunity.exchange1_market == 'futures' else opportunity.exchange2_price

            # 计算所需资金
            required_spot_usdt = base_amount * spot_price  # 现货买入需要USDT
            required_futures_margin = (base_amount * futures_price) / 2  # 期货保证金（假设2倍杠杆）

            # 🔥 使用余额缓存进行零延迟检查
            # 🔥 关键修复：正确处理统一账户的缓存key

            # 现货余额检查 - 修复OKX统一账户问题
            if spot_exchange in ["bybit", "okx"]:  # 统一账户交易所
                spot_cache_key = f"{spot_exchange}_unified_usdt"
            else:
                spot_cache_key = f"{spot_exchange}_spot_usdt"

            available_spot_usdt = self.balance_cache.get(spot_cache_key, 0.0)

            # 🔥 记录余额缓存命中/未命中
            if spot_cache_key in self.balance_cache:
                log_balance_hit(spot_exchange, spot_cache_key, available_spot_usdt)
            else:
                log_balance_miss(spot_exchange, spot_cache_key)

            # 期货余额检查 - 修复OKX统一账户问题
            if futures_exchange in ["bybit", "okx"]:  # 统一账户交易所
                futures_cache_key = f"{futures_exchange}_unified_usdt"
            else:
                futures_cache_key = f"{futures_exchange}_futures_usdt"

            available_futures_usdt = self.balance_cache.get(futures_cache_key, 0.0)

            # 🔥 记录余额缓存命中/未命中
            if futures_cache_key in self.balance_cache:
                log_balance_hit(futures_exchange, futures_cache_key, available_futures_usdt)
            else:
                log_balance_miss(futures_exchange, futures_cache_key)

            # 余额充足性检查（使用90%安全边际）
            spot_sufficient = available_spot_usdt * 0.9 >= required_spot_usdt
            futures_sufficient = available_futures_usdt * 0.9 >= required_futures_margin

            self.logger.debug(f"余额缓存检查: {symbol}")
            self.logger.debug(f"  现货({spot_exchange}): key={spot_cache_key}, 需要${required_spot_usdt:.2f}, 可用${available_spot_usdt:.2f}, 充足={spot_sufficient}")
            self.logger.debug(f"  期货({futures_exchange}): key={futures_cache_key}, 需要${required_futures_margin:.2f}, 可用${available_futures_usdt:.2f}, 充足={futures_sufficient}")

            # 🔥 OKX专门调试：详细记录缓存状态
            if 'okx' in spot_exchange.lower() or 'okx' in futures_exchange.lower():
                self.logger.info(f"🔥 OKX余额验证详情: {symbol}")
                self.logger.info(f"   现货缓存key: {spot_cache_key} = ${available_spot_usdt:.2f}")
                self.logger.info(f"   期货缓存key: {futures_cache_key} = ${available_futures_usdt:.2f}")
                self.logger.info(f"   所有缓存keys: {list(self.balance_cache.keys())}")
                self.logger.info(f"   现货验证: 需要${required_spot_usdt:.2f} vs 可用${available_spot_usdt:.2f} = {spot_sufficient}")
                self.logger.info(f"   期货验证: 需要${required_futures_margin:.2f} vs 可用${available_futures_usdt:.2f} = {futures_sufficient}")

            if not spot_sufficient:
                self.logger.debug(f"  ❌ 现货余额不足: {spot_exchange}")
                return False

            if not futures_sufficient:
                self.logger.debug(f"  ❌ 期货余额不足: {futures_exchange}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"余额缓存检查异常: {e}")
            return False

    async def _validate_margin_from_cache(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 按照全流程工作流.md第2阶段：零延迟验证
        🚀 保证金检查（0.00ms）：使用MarginCalculator保证金缓存
        """
        try:
            from utils.margin_calculator import MarginCalculator
            margin_calc = MarginCalculator()

            futures_exchange = opportunity.sell_exchange
            symbol = opportunity.symbol
            base_amount = opportunity.base_amount

            # 获取期货价格
            futures_price = opportunity.exchange1_price if opportunity.exchange1_market == 'futures' else opportunity.exchange2_price

            # 🔥 修复：进行实际保证金计算，而不是只检查缓存存在性
            # 获取对应的交易所客户端
            exchange_client = None
            if hasattr(self, 'exchanges') and futures_exchange in self.exchanges:
                exchange_client = self.exchanges[futures_exchange]

            # 计算所需保证金
            required_margin, margin_details = await margin_calc.calculate_required_margin(
                futures_exchange, symbol, base_amount, futures_price, exchange_client
            )

            # 检查可用保证金
            sufficient, available_margin, balance_details = await margin_calc.check_available_margin(
                futures_exchange, required_margin, exchange_client
            )

            if sufficient:
                self.logger.debug(f"保证金检查通过: {symbol} 需要${required_margin:.2f}, 可用${available_margin:.2f}")
                return True
            else:
                self.logger.warning(f"保证金不足: {symbol} 需要${required_margin:.2f}, 可用${available_margin:.2f}")
                return False

        except Exception as e:
            self.logger.error(f"保证金检查异常: {e}")
            # 🔥 保守策略：检查失败时返回False，避免保证金不足的交易
            return False

    def _validate_precision_from_cache(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 按照全流程工作流.md第2阶段：零延迟验证
        🚀 精度数据（0.00ms）：使用TradingRulesPreloader精度缓存
        """
        try:
            rules_preloader = self.rules_preloader

            spot_exchange = opportunity.buy_exchange
            futures_exchange = opportunity.sell_exchange
            symbol = opportunity.symbol

            # 🔥 检查精度缓存数据
            spot_rule = rules_preloader.get_trading_rule(spot_exchange, symbol, "spot")
            futures_rule = rules_preloader.get_trading_rule(futures_exchange, symbol, "futures")

            if not spot_rule:
                self.logger.debug(f"现货精度缓存缺失: {spot_exchange} {symbol}")
                return False

            if not futures_rule:
                self.logger.debug(f"期货精度缓存缺失: {futures_exchange} {symbol}")
                return False

            self.logger.debug(f"精度缓存检查通过: 现货{spot_rule.qty_precision}位, 期货{futures_rule.qty_precision}位")
            return True

        except Exception as e:
            self.logger.error(f"精度数据缓存检查异常: {e}")
            return False

    def _validate_rules_from_cache(self, opportunity: ArbitrageOpportunity) -> bool:
        """
        🔥 按照全流程工作流.md第2阶段：零延迟验证
        🚀 交易规则（0.00ms）：使用TradingRulesPreloader交易规则缓存
        """
        try:
            rules_preloader = self.rules_preloader

            spot_exchange = opportunity.buy_exchange
            futures_exchange = opportunity.sell_exchange
            symbol = opportunity.symbol
            base_amount = opportunity.base_amount

            # 🔥 检查交易规则缓存数据
            spot_rule = rules_preloader.get_trading_rule(spot_exchange, symbol, "spot")
            futures_rule = rules_preloader.get_trading_rule(futures_exchange, symbol, "futures")

            if not spot_rule or not futures_rule:
                return False  # 已在精度检查中验证

            # 检查最小订单量要求
            if base_amount < spot_rule.min_qty:
                self.logger.debug(f"现货订单量不满足最小要求: {base_amount} < {spot_rule.min_qty}")
                return False

            if base_amount < futures_rule.min_qty:
                self.logger.debug(f"期货订单量不满足最小要求: {base_amount} < {futures_rule.min_qty}")
                return False

            # 检查最小名义价值要求
            spot_value = base_amount * opportunity.exchange1_price if opportunity.exchange1_market == 'spot' else base_amount * opportunity.exchange2_price
            if spot_value < spot_rule.min_notional:
                self.logger.debug(f"现货订单价值不满足最小要求: ${spot_value:.2f} < ${spot_rule.min_notional:.2f}")
                return False

            self.logger.debug(f"交易规则缓存检查通过: 订单量{base_amount:.8f}, 订单价值${spot_value:.2f}")
            return True

        except Exception as e:
            self.logger.error(f"交易规则缓存检查异常: {e}")
            return False

    def _validate_depth(self, opportunity: ArbitrageOpportunity) -> bool:
        """验证深度充足性 - 简化版本"""
        try:
            # 简化深度验证：直接返回True，让实际执行时处理深度不足的情况
            # 这样避免了复杂的深度检查逻辑，在实际执行中处理风险
            self.logger.debug(f"深度验证: {opportunity.symbol} - 简化检查通过")
            return True

        except Exception as e:
            self.logger.error(f"Error validating depth: {e}")
            return False

    async def _start_arbitrage_session(self, opportunity: ArbitrageOpportunity):
        """开始套利会话"""
        try:
            session_id = f"ARB_{int(time.time())}"

            # 生成组合名称
            combination_name = f"{opportunity.buy_exchange}现货-{opportunity.sell_exchange}期货"

            # 🔥 修复：使用异步锁的正确方式
            async with self.lock:
                # 🔥 修复：统一状态设置 - 确保is_executing和current_status同步
                self.is_executing = True
                self.current_status = ArbitrageStatus.PREPARING

                self.current_session = ArbitrageSession(
                    session_id=session_id,
                    opportunity=opportunity,
                    status=ArbitrageStatus.PREPARING,
                    start_time=time.time()
                )

                self.total_sessions += 1

            self.logger.debug(f"✅ 执行状态已设置: is_executing = True, current_status = PREPARING")

            self.logger.info(f"Starting arbitrage session {session_id}")
            self.logger.info(f"  Combination: {combination_name}")
            self.logger.info(f"  Symbol: {opportunity.symbol}")
            self.logger.info(f"  Spread: {opportunity.spread_percent*100:.4f}%")
            self.logger.info(f"  Estimated profit: {format_currency(opportunity.profit_estimate)}")

        except Exception as e:
            self.logger.error(f"Error starting arbitrage session: {e}")
            raise

    async def _prepare_funds(self):
        """准备资金"""
        try:
            if not self.current_session:
                return

            opportunity = self.current_session.opportunity

            # 根据套利组合准备资金
            spot_exchange = opportunity.exchange1_name if opportunity.exchange1_market == "spot" else opportunity.exchange2_name
            futures_exchange = opportunity.exchange1_name if opportunity.exchange1_market == "futures" else opportunity.exchange2_name
            
            # 使用现货价格计算所需USDT金额
            spot_price = opportunity.exchange1_price if opportunity.exchange1_market == "spot" else opportunity.exchange2_price
            required_amount = opportunity.base_amount * spot_price
            
            combinations = [{
                "spot_exchange": spot_exchange,
                "futures_exchange": futures_exchange,
                "amount": required_amount
            }]
            
            await self.fund_manager.prepare_arbitrage_funds(combinations)

            self.logger.info(f"Funds prepared for session {self.current_session.session_id}")

        except Exception as e:
            self.logger.error(f"Error preparing funds: {e}")
            raise

    def _update_session_from_execution(self, execution_result):
        """从执行结果更新会话"""
        try:
            if not self.current_session:
                return

            # ExecutionResult对象有以下属性：
            # - success: bool
            # - spot_orders: List[ExecutionOrder]  
            # - futures_orders: List[ExecutionOrder]
            # - spot_filled: float
            # - futures_filled: float
            # - execution_time: float
            # - error: Optional[str]
            
            # 如果是字典格式，需要转换处理
            if isinstance(execution_result, dict):
                self.current_session.spot_orders = execution_result.get('spot_orders', [])
                self.current_session.futures_orders = execution_result.get('futures_orders', [])
                self.current_session.total_spot_filled = execution_result.get('spot_filled', 0.0)
                self.current_session.total_futures_filled = execution_result.get('futures_filled', 0.0)
            else:
                # ExecutionResult对象格式
                # 🔥 修复：添加兼容性处理，确保即使ExecutionResult没有这些属性也能正常工作
                try:
                    # 转换ExecutionOrder对象为字典格式存储
                    spot_orders = getattr(execution_result, 'spot_orders', [])
                    futures_orders = getattr(execution_result, 'futures_orders', [])
                    spot_filled = getattr(execution_result, 'spot_filled', 0.0)
                    futures_filled = getattr(execution_result, 'futures_filled', 0.0)

                    self.current_session.spot_orders = [
                        {
                            'id': order.order_id,
                            'status': order.status,
                            'filled': order.filled,
                            'average_price': order.average_price,
                            'price': order.price,
                            'amount': order.amount
                        } for order in spot_orders
                    ]

                    self.current_session.futures_orders = [
                        {
                            'id': order.order_id,
                            'status': order.status,
                            'filled': order.filled,
                            'average_price': order.average_price,
                            'price': order.price,
                            'amount': order.amount
                        } for order in futures_orders
                    ]

                    self.current_session.total_spot_filled = spot_filled
                    self.current_session.total_futures_filled = futures_filled

                except AttributeError as e:
                    # 🔥 兼容性处理：如果ExecutionResult没有这些属性，使用默认值
                    self.logger.warning(f"ExecutionResult缺少订单属性，使用默认值: {e}")
                    self.current_session.spot_orders = []
                    self.current_session.futures_orders = []
                    self.current_session.total_spot_filled = 0.0
                    self.current_session.total_futures_filled = 0.0

            self.logger.info(f"Session {self.current_session.session_id} execution updated")
            self.logger.info(f"  Spot filled: {self.current_session.total_spot_filled}")
            self.logger.info(f"  Futures filled: {self.current_session.total_futures_filled}")

        except Exception as e:
            self.logger.error(f"Error updating session from execution: {e}")
            self.logger.error(f"  execution_result type: {type(execution_result)}")
            self.logger.error(f"  execution_result: {execution_result}")

    async def _calculate_session_profit(self):
        """计算会话收益 - 🔥 修复：避免除零错误"""
        try:
            if not self.current_session:
                return

            # 🔥 修复：检查是否有实际的订单数据
            if not self.current_session.spot_orders and not self.current_session.futures_orders:
                self.logger.info("无实际交易订单，跳过利润计算")
                self.current_session.realized_profit = 0.0
                return

            # 获取实际成交价格和数量
            spot_avg_price = self._calculate_average_price(self.current_session.spot_orders)
            futures_avg_price = self._calculate_average_price(self.current_session.futures_orders)

            # 🔥 修复：检查价格有效性，避免除零错误
            if spot_avg_price <= 0 or futures_avg_price <= 0:
                self.logger.warning(f"价格数据无效，跳过利润计算: spot_price={spot_avg_price}, futures_price={futures_avg_price}")
                self.current_session.realized_profit = 0.0
                return

            # 🔥 修复：检查成交量有效性
            volume = min(self.current_session.total_spot_filled, self.current_session.total_futures_filled)
            if volume <= 0:
                self.logger.warning(f"成交量无效，跳过利润计算: volume={volume}")
                self.current_session.realized_profit = 0.0
                return

            # 🔥 修复：使用统一Order差价计算模块进行精确利润计算
            try:
                # 获取最新的订单簿数据进行精确计算
                from core.opportunity_scanner import get_opportunity_scanner
                scanner = get_opportunity_scanner()

                if scanner and self.current_session:
                    # 构建市场数据键
                    symbol = self.current_session.opportunity.symbol
                    spot_exchange = self.current_session.opportunity.buy_exchange if self.current_session.opportunity.buy_market == "spot" else self.current_session.opportunity.sell_exchange
                    futures_exchange = self.current_session.opportunity.sell_exchange if self.current_session.opportunity.sell_market == "futures" else self.current_session.opportunity.buy_exchange

                    spot_key = f"{spot_exchange}_spot_{symbol}"
                    futures_key = f"{futures_exchange}_futures_{symbol}"

                    # 获取最新订单簿数据
                    spot_data = scanner.market_data.get(spot_key)
                    futures_data = scanner.market_data.get(futures_key)

                    if spot_data and futures_data:
                        spot_orderbook = spot_data.orderbook if hasattr(spot_data, 'orderbook') and spot_data.orderbook else {}
                        futures_orderbook = futures_data.orderbook if hasattr(futures_data, 'orderbook') and futures_data.orderbook else {}

                        # 🔥 关键修复：根据实际交易方向动态判断execution_context
                        # 期货溢价套利：买现货+卖期货 = opening
                        # 现货溢价套利：卖现货+买期货 = closing
                        if self.current_session.opportunity.buy_market == "spot":
                            execution_context = "opening"  # 期货溢价开仓
                        else:
                            execution_context = "closing"  # 现货溢价平仓

                        # 🔥 修复：使用统一Order差价计算器进行精确利润计算
                        from core.unified_order_spread_calculator import get_order_spread_calculator
                        calculator = get_order_spread_calculator()
                        order_result = calculator.calculate_order_based_spread_with_snapshot(
                            spot_orderbook, futures_orderbook, volume * spot_avg_price, execution_context, force_snapshot=True
                        )
                        actual_spread = order_result.executable_spread if order_result else None

                        if actual_spread is not None:
                            actual_spread = abs(actual_spread)  # 利润计算使用绝对值
                            self.logger.debug(f"✅ Order精确利润计算价差: {actual_spread*100:.3f}%")
                        else:
                            # 🔥 修复：不降级，直接使用0表示计算失败
                            actual_spread = 0.0
                            self.logger.warning(f"⚠️ Order差价计算返回None，利润计算设为0")
                    else:
                        # 🔥 修复：不降级，直接使用0表示数据不可用
                        actual_spread = 0.0
                        self.logger.warning(f"⚠️ 订单簿数据不可用，利润计算设为0")
                else:
                    # 🔥 修复：不降级，直接使用0表示scanner不可用
                    actual_spread = 0.0
                    self.logger.warning(f"⚠️ OpportunityScanner不可用，利润计算设为0")

            except Exception as e:
                self.logger.error(f"❌ Order差价计算失败: {e}，利润计算设为0")
                # 🔥 修复：不降级，直接使用0表示计算异常
                actual_spread = 0.0
            self.current_session.realized_profit = volume * actual_spread * spot_avg_price
            self.total_profit += self.current_session.realized_profit

            self.logger.info(f"Session {self.current_session.session_id} profit calculated")
            self.logger.info(f"  Realized profit: {format_currency(self.current_session.realized_profit)}")
            self.logger.info(f"  Actual spread: {calculate_percentage(actual_spread):.2f}%")
            self.logger.info(f"  Volume: {volume:.6f}")
            self.logger.info(f"  Spot avg price: ${spot_avg_price:.6f}")
            self.logger.info(f"  Futures avg price: ${futures_avg_price:.6f}")

        except Exception as e:
            self.logger.error(f"Error calculating session profit: {e}")
            # 🔥 修复：确保在出错时也设置利润为0
            if self.current_session:
                self.current_session.realized_profit = 0.0

    def _calculate_average_price(self, orders: List[Dict]) -> float:
        """计算平均成交价格"""
        try:
            if not orders:
                return 0.0

            total_amount = 0.0
            total_quantity = 0.0

            for order in orders:
                if order.get('status') == 'filled':
                    quantity = float(order.get('filled', 0))
                    price = float(order.get('average_price', order.get('price', 0)))

                    total_amount += quantity * price
                    total_quantity += quantity

            return total_amount / total_quantity if total_quantity > 0 else 0.0

        except Exception as e:
            self.logger.error(f"Error calculating average price: {e}")
            return 0.0

    def _finalize_session(self):
        """完成会话"""
        try:
            if not self.current_session:
                return

            self.current_session.end_time = time.time()
            self.current_session.status = ArbitrageStatus.COMPLETED

            # 更新统计
            if self.current_session.realized_profit > 0:
                self.successful_sessions += 1

            duration = self.current_session.end_time - self.current_session.start_time

            self.logger.info(f"Session {self.current_session.session_id} finalized")
            self.logger.info(f"  Duration: {duration:.2f}s")
            self.logger.info(f"  Final profit: {format_currency(self.current_session.realized_profit)}")
            self.logger.info(f"  Success rate: {self.successful_sessions}/{self.total_sessions}")

            # 清除当前会话
            self.current_session = None

        except Exception as e:
            self.logger.error(f"Error finalizing session: {e}")

    async def _force_close_session(self):
        """强制关闭会话"""
        try:
            if not self.current_session:
                return

            self.logger.warning(f"Force closing session {self.current_session.session_id}")

            # 尝试平仓
            await self.execution_engine.force_close_all_positions()

            # 标记会话为错误状态
            self.current_session.status = ArbitrageStatus.ERROR
            self.current_session.end_time = time.time()

        except Exception as e:
            self.logger.error(f"Error force closing session: {e}")

    async def _cleanup_components(self):
        """清理组件"""
        try:
            # 清理各组件 - 安全检查
            components = [
                ('execution_engine', self.execution_engine),
                ('opportunity_scanner', self.opportunity_scanner),
                ('fund_manager', self.fund_manager),
                ('position_monitor', self.position_monitor),
                ('risk_monitor', self.risk_monitor)
            ]

            for name, component in components:
                try:
                    if component and hasattr(component, 'cleanup'):
                        await component.cleanup()
                        self.logger.debug(f"{name} cleaned up successfully")
                    else:
                        self.logger.debug(f"{name} skip cleanup (not initialized or no cleanup method)")
                except Exception as e:
                    self.logger.warning(f"Error cleaning up {name}: {e}")

            self.logger.info("All components cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up components: {e}")

    def get_status(self) -> Dict:
        """获取引擎状态"""
        try:
            status = {
                'running': self.running,
                'current_status': self.current_status.value,
                'total_sessions': self.total_sessions,
                'successful_sessions': self.successful_sessions,
                'success_rate': self.successful_sessions / self.total_sessions if self.total_sessions > 0 else 0,
                'total_profit': self.total_profit,
                'current_session': None
            }

            if self.current_session:
                # 生成组合名称
                opportunity = self.current_session.opportunity
                combination_name = f"{opportunity.buy_exchange}现货-{opportunity.sell_exchange}期货"
                
                status['current_session'] = {
                    'session_id': self.current_session.session_id,
                    'combination': combination_name,
                    'symbol': self.current_session.opportunity.symbol,
                    'spread': self.current_session.opportunity.spread_percent*100,
                    'duration': time.time() - self.current_session.start_time,
                    'status': self.current_session.status.value
                }

            return status

        except Exception as e:
            self.logger.error(f"Error getting status: {e}")
            return {'error': str(e)}


# 🔥 全局单例访问 - 修复导入错误
_arbitrage_engine_instance = None

def get_arbitrage_engine() -> Optional[ArbitrageEngine]:
    """获取套利引擎实例，如果不存在则自动创建"""
    global _arbitrage_engine_instance
    if _arbitrage_engine_instance is None:
        try:
            _arbitrage_engine_instance = ArbitrageEngine()
        except Exception as e:
            from utils.logger import get_logger
            logger = get_logger(__name__)
            logger.error(f"❌ ArbitrageEngine初始化失败: {e}")
            return None
    return _arbitrage_engine_instance

def set_arbitrage_engine(engine: ArbitrageEngine):
    """设置套利引擎实例"""
    global _arbitrage_engine_instance
    _arbitrage_engine_instance = engine


if __name__ == '__main__':
    """单独运行测试"""
    import asyncio
    from config.debug_config import DEBUG

    async def test_arbitrage_engine():
        engine = ArbitrageEngine()

        try:
            await engine.start()

            # 运行一段时间用于测试
            await asyncio.sleep(60)

        except KeyboardInterrupt:
            pass  # 静默处理用户中断
        finally:
            await engine.stop()

    if DEBUG:
        asyncio.run(test_arbitrage_engine())