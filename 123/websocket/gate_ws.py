#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io WebSocket客户端
支持现货和期货的深度数据
"""

import asyncio
import json
import time
from typing import Dict, Any, List
import logging
from datetime import datetime
import sys
import os

# 尝试导入基类和自定义日志系统
try:
    from websocket.ws_client import WebSocketClient
    logger = logging.getLogger("websocket.gate")
except ImportError:
    # 开发环境导入路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from websocket.ws_client import WebSocketClient
    logger = logging.getLogger("websocket.gate")


class GateWebSocketClient(WebSocketClient):
    """Gate.io WebSocket客户端"""
    
    def __init__(self, market_type: str = "spot", settings=None):
        """
        初始化
        :param market_type: 市场类型 "spot" 或 "futures"
        :param settings: 全局配置，可选
        """
        super().__init__("GATE", settings)
        self.market_type = market_type
        self.symbols = []  # 交易对列表

        # 🔥 **100%修复**：优先设置logger属性
        self.logger = logger  # 确保logger属性在所有使用之前都可用
        
        # 🔥 时间同步机制 - 使用统一时间戳处理器
        from websocket.unified_timestamp_processor import get_timestamp_processor
        self.timestamp_processor = get_timestamp_processor("gate")

        # 🔥 **100%完美修复**：集成统一连接池管理器
        self._integrated_with_pool = False
        self.pool_connection_id = None
        try:
            from websocket.unified_connection_pool_manager import get_connection_pool_manager
            self.pool_manager = get_connection_pool_manager()
            self._integrated_with_pool = True
            self.pool_connection_id = f"gate_{market_type}_websocket"
            self.logger.info("✅ Gate.io已集成统一连接池管理器")
        except Exception as e:
            self.logger.warning(f"⚠️ Gate.io统一连接池管理器集成失败: {e}")

        # 保留这些属性以兼容现有代码
        self.time_offset = 0  # 时间偏移（毫秒）
        self.time_synced = False
        self.last_sync_time = 0

        # Gate.io WebSocket URLs
        self.ws_urls = {
            "spot": "wss://api.gateio.ws/ws/v4/",
            "futures": "wss://fx-ws.gateio.ws/v4/ws/usdt"
        }
        
        # 🚀 优化：只订阅必要的数据类型，删除不必要的trades
        self.channels = ["order_book"]  # 只需要深度数据
        
        # 统计收到的各类消息数量
        self.orderbook_count = 0
        self.trade_count = 0
        
        # 🔥 **删除重复的数据流监控**：完全由enhanced_blocking_tracker处理
        # 避免与统一阻塞追踪器的功能重复
        
        # 🔥 统一修复：集成增强的阻塞追踪器
        try:
            from websocket.enhanced_blocking_tracker import log_websocket_data_received, log_websocket_subscription_attempt
            self._log_data_received = log_websocket_data_received
            self._log_subscription_attempt = log_websocket_subscription_attempt
        except ImportError:
            self._log_data_received = lambda *args, **kwargs: None
            self._log_subscription_attempt = lambda *args, **kwargs: None
        
        # 🔥 统一异步锁机制：采用Bybit/OKX简化策略
        self.orderbook_locks = {}  # 简化锁管理，与Bybit/OKX一致
        
        # 🔥 **统一阈值修复**：统一三交易所心跳间隔和连接超时
        self.heartbeat_interval = 20  # 🔥 统一20秒心跳间隔，与network_config.py一致
        self.connection_timeout = 10  # 🔥 统一10秒连接超时，与network_config.py一致
        self.connect_warning_threshold = 3.0  # 连接警告阈值
        
        self._log_debug(f"初始化 {market_type} WebSocket客户端")
        
    def get_ws_url(self) -> str:
        """获取WebSocket URL"""
        url = self.ws_urls.get(self.market_type, self.ws_urls["spot"])
        self._log_debug(f"使用WebSocket URL: {url}")
        return url
    
    def set_symbols(self, symbols: List[str]):
        """设置要监控的交易对 - 🔧 集成统一交易对验证机制"""
        if not symbols:
            self._log_warning("交易对列表为空，将使用默认交易对")
            symbols = ["ADA-USDT", "SOL-USDT"]  # 默认兜底交易对
        
        # 🔧 **通用系统修复**：集成统一交易对验证机制
        try:
            from core.unified_symbol_validator import get_symbol_validator
            validator = get_symbol_validator()
            
            # 🔧 智能过滤：只保留Gate.io支持的交易对
            supported_symbols = validator.filter_supported_symbols(symbols, "gate", self.market_type)
            
            if not supported_symbols:
                self._log_warning("⚠️ 经过Gate.io验证，没有支持的交易对，使用默认交易对")
                supported_symbols = ["ADA_USDT", "SOL_USDT"]  # Gate.io格式兜底
            
            self.symbols = supported_symbols
            self._log_info(f"✅ Gate.io {self.market_type} 设置验证后的交易对: {', '.join(supported_symbols)}")
            
        except ImportError:
            # 🔧 兜底处理：验证器不可用时使用原逻辑
            converted_symbols = []
            for symbol in symbols:
                try:
                    # 统一格式转换
                    if '-' in symbol:
                        gate_symbol = symbol.replace('-', '_')
                    elif '_' in symbol:
                        gate_symbol = symbol  # 已经是Gate格式
                    else:
                        # BTCUSDT -> BTC_USDT
                        if symbol.endswith('USDT') and len(symbol) > 4:
                            base = symbol[:-4]
                            gate_symbol = f"{base}_USDT"
                        else:
                            gate_symbol = symbol
                    
                    converted_symbols.append(gate_symbol)
                    
                except Exception as e:
                    self._log_warning(f"转换交易对 {symbol} 时发生错误: {e}，跳过该交易对")
                    continue
            
            # 如果转换后没有任何交易对，使用默认
            if not converted_symbols:
                converted_symbols = ["ADA_USDT"]  # 最终后备选项
                
            self.symbols = converted_symbols
            self._log_info(f"Gate.io {self.market_type} 支持的交易对: {', '.join(converted_symbols)}")
            
        except Exception as e:
            self._log_error(f"交易对验证失败: {e}，使用基础转换逻辑")
            # 最终兜底
            converted_symbols = []
            for symbol in symbols:
                gate_symbol = symbol.replace('-', '_').upper()
                converted_symbols.append(gate_symbol)
            self.symbols = converted_symbols if converted_symbols else ["ADA_USDT"]

    async def _sync_time(self):
        """🔥 已删除：使用统一时间戳处理器替代"""
        # 这个方法已被统一时间戳处理器替代，保留空实现以兼容
        pass



    async def run(self):
        """运行WebSocket客户端 - 🔥 修复：移除独立时间同步，使用集中式同步"""
        # 🔥 关键修复：移除WebSocket客户端的独立时间同步调用
        # 时间同步现在由系统启动时的集中式同步管理，避免并发冲突

        # 🔥 **CRITICAL修复**：使用实例方法调用，确保三交易所一致性
        # 仅检查同步状态，不执行同步操作
        if self.timestamp_processor.time_synced:
            self._log_info(f"✅ Gate.io时间已同步，偏移量: {self.timestamp_processor.time_offset}ms")
        else:
            self._log_warning(f"⚠️ Gate.io时间未同步，将使用统一时间基准")

        # 调用父类的run方法
        await super().run()

    async def subscribe_channels(self):
        """订阅频道 - 🔥 修复：深度订阅必须逐个，不能批量（Gate.io API限制）"""
        if not self.symbols:
            self._log_error("没有设置交易对，无法订阅")
            return False
            
        try:
            self._log_info(f"🚀 开始订阅 {len(self.symbols)} 个交易对")
            
            # 🔥 TICKER订阅已完全移除 - 系统只使用OrderBook数据
            self._log_info(f"🚀 只订阅OrderBook数据，ticker订阅已移除")
            
            # 🔥 第二步：深度数据订阅（模拟批量处理保持一致性）
            self._log_info(f"📈 开始批量订阅深度数据...")
            
            depth_success_count = 0
            batch_size = 1  # Gate.io必须逐个订阅，但保持批量架构一致性
            for i, symbol in enumerate(self.symbols):
                self._log_info(f"📊 订阅深度 {i+1}/{len(self.symbols)}: {symbol}")
                
                # 🔥 **回退错误修改**：必须使用order_book获取完整深度数据
                if self.market_type == "spot":
                    depth_msg = {
                        "time": int(time.time()),
                        "channel": "spot.order_book",
                        "event": "subscribe",
                        "payload": [symbol, "50", "100ms"]  # 🔥 保持原有格式，这是正确的完整深度订阅
                    }
                else:
                    # 🔥 **官方API规范修复**：期货使用1参数格式
                    depth_msg = {
                        "time": int(time.time()),
                        "channel": "futures.order_book",
                        "event": "subscribe",
                        "payload": [symbol]  # 🔥 **修复**：期货使用1参数格式 [symbol]
                    }
                
                self._log_debug(f"发送{symbol}深度订阅: {json.dumps(depth_msg)}")
                depth_success = await self.send(depth_msg)
                
                if depth_success:
                    depth_success_count += 1
                    self._log_info(f"✅ {symbol} 深度订阅成功")
                else:
                    # 🔥 **统一错误处理风格**：与其他交易所保持一致
                    self._log_error(f"❌ [GATE-{self.market_type.upper()}] {symbol} 深度订阅失败")

                    # 🔥 新增：记录订阅失败日志
                    from .websocket_logger import log_websocket_subscription_failure
                    log_websocket_subscription_failure("error", f"深度订阅失败",
                                                     exchange="gate", symbol=symbol,
                                                     market_type=self.market_type, channel="order_book")
                
                # 🔥 **官方API规范修复**：基于官方文档的频率限制要求
                # Gate.io官方文档：需要逐个订阅，使用适度间隔避免频率限制
                await asyncio.sleep(0.1)  # **修复：优化为0.1秒，平衡效率和稳定性**
                
            self._log_info(f"🎯 Gate {self.market_type} 深度订阅完成: {depth_success_count}/{len(self.symbols)} 成功")
            
            # 只要Ticker订阅成功就算成功（深度数据不是必须的）
            return True
            
        except Exception as e:
            self._log_error(f"订阅频道时发生异常: {e}", exc_info=True)
            return False
    
    async def handle_message(self, message: Dict[str, Any]):
        """处理消息 - 增强版本，支持所有可能的消息格式"""
        try:
            channel = message.get("channel", "")
            event = message.get("event", "")

            # 🔥 详细记录所有消息用于调试
            self._log_debug(f"🔍 [GATE-{self.market_type.upper()}] 收到消息: channel={channel}, event={event}")

            # 🔥 完美修复：移除消息处理限流，避免重要订单簿更新丢失
            # 原有的100ms限流可能导致数据丢失，违反数据完整性要求
            # 使用基类的消息处理机制，确保所有数据都能正确处理

            # 处理订阅响应
            if event == "subscribe":
                result = message.get("result", {})
                status = result.get("status", "")
                if status == "success":
                    self._log_info(f"✅ [GATE-{self.market_type.upper()}] 订阅成功: {channel}")
                else:
                    # 🔧 **通用系统修复**：智能处理订阅失败，不影响整体运行
                    error_info = message.get("error", {})
                    error_code = error_info.get("code", "") if error_info else ""
                    error_msg = error_info.get("message", "") if error_info else str(message)
                    
                    # 🔧 **通用系统支持任意代币修复**：基于官方API规范的智能过滤
                    if error_code == 2 and ("unknown currency pair" in error_msg.lower() or 
                                           "invalid symbol" in error_msg.lower() or
                                           "currency pair not found" in error_msg.lower() or
                                           "unsupported currency pair" in error_msg.lower() or
                                           "not found" in error_msg.lower()):
                        # 🔥 **统一智能过滤日志风格**：交易对不存在是正常的，通用系统应自动过滤
                        self._log_debug(f"🔧 [GATE-{self.market_type.upper()}] 智能过滤不支持的交易对: {error_msg}")
                        
                        # 🔧 动态添加到不支持列表
                        try:
                            from core.unified_symbol_validator import get_symbol_validator
                            validator = get_symbol_validator()
                            if "currency pair" in error_msg:
                                symbol_match = error_msg.split("currency pair")[-1].strip()
                                if symbol_match:
                                    validator.add_unsupported_symbol(symbol_match, "gate")
                        except Exception:
                            pass  # 静默处理，不影响主流程
                        
                        return  # 不记录为错误，优雅降级
                    else:
                        # 🔥 **统一错误处理风格**：与OKX、Bybit保持一致的格式
                        self._log_error(f"❌ [GATE-{self.market_type.upper()}] WebSocket错误 [{error_code}]: {error_msg}")
                        # 🔥 记录真正的订阅失败日志
                        from .websocket_logger import log_websocket_subscription_failure
                        log_websocket_subscription_failure("error", f"订阅响应失败",
                                                         exchange="gate", channel=channel,
                                                         market_type=self.market_type,
                                                         error_code=error_code,
                                                         error_message=error_msg)
                return

            # 🔥 **统一错误处理风格**：处理错误消息
            if event == "error":
                self._log_error(f"❌ [GATE-{self.market_type.upper()}] WebSocket错误消息: {message}")
                return

            # 处理服务器ping请求
            if event == "ping":
                # 回复pong，避免服务器断开连接
                pong_msg = {"event": "pong", "time": int(time.time())}
                await self.send(pong_msg)
                self._log_debug("💓 [GATE] 收到ping，已回复pong")
                return

            # 🔥 增强的数据处理逻辑 - 支持多种消息格式

            # 格式1：标准update事件
            if event == "update":
                result = message.get("result", {})

                if "tickers" in channel:
                    # 🔥 ticker数据已完全移除，跳过处理
                    self._log_debug(f"跳过ticker数据: {channel}")
                elif "order_book" in channel:  # 🔥 回退：只处理order_book，不处理book_ticker
                    self.orderbook_count += 1
                    await self._handle_orderbook(result)
                    self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理完整订单簿数据")
                elif "trades" in channel:
                    self.trade_count += 1
                    await self._handle_trades(result)
                    self._log_debug(f"💱 [GATE-{self.market_type.upper()}] 处理交易数据")

            # 🔥 ticker数据处理已完全移除
            elif "tickers" in channel and not event:
                self._log_debug(f"跳过ticker数据: {channel}")

            # 格式3：直接的订单簿数据（无event字段）
            elif "order_book" in channel and not event:  # 🔥 修复：处理全量订单簿
                self.orderbook_count += 1
                result = message.get("result", None)
                if result:
                    await self._handle_orderbook(result)
                    self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理直接订单簿数据")
                else:
                    self._log_warning(f"⚠️ [GATE-{self.market_type.upper()}] 订单簿消息无result字段")

            # 🔥 格式4：其他可能的订单簿消息格式
            elif any(keyword in channel.lower() for keyword in ["orderbook", "order_book", "depth", "book"]):
                self.orderbook_count += 1
                # 尝试多种可能的数据字段
                result = message.get("result", message.get("data", message))
                await self._handle_orderbook(result)
                self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理其他格式订单簿数据: {channel}")

            # 🔥 格式5：如果消息包含订单簿相关字段，直接处理
            elif any(field in message for field in ["a", "b", "asks", "bids"]):
                self.orderbook_count += 1
                await self._handle_orderbook(message)
                self._log_info(f"📖 [GATE-{self.market_type.upper()}] 处理包含订单簿字段的消息")

            # 🔥 未识别的消息格式 - 记录用于调试
            else:
                self._log_debug(f"🤔 [GATE-{self.market_type.upper()}] 未识别消息格式: channel={channel}, event={event}")
                self._log_debug(f"   消息keys: {list(message.keys())}")
                self._log_debug(f"   消息内容: {str(message)[:200]}...")

        except Exception as e:
            # 🔥 **统一异常处理风格**：与其他交易所保持一致的异常处理格式
            self._log_error(f"❌ [GATE-{self.market_type.upper()}] 处理消息异常: {str(e)}", exc_info=True)
            self._log_error(f"🔍 [GATE-{self.market_type.upper()}] 消息内容: {str(message)[:300]}")  # 记录更多消息内容用于调试
    
    # 🔥 TICKER处理方法已完全删除 - 系统只使用OrderBook数据
    
    async def _handle_orderbook(self, data: Dict[str, Any]):
        """🔥 Gate.io官方API规范修复：快照模式处理（与Bybit/OKX增量模式不同）"""
        # 🔥 预先导入，避免循环内重复导入
        import time
        import asyncio
        from websocket.orderbook_validator import get_orderbook_validator
        from websocket.performance_monitor import record_message_latency
        from exchanges.currency_adapter import normalize_symbol
        from websocket.unified_data_formatter import get_orderbook_formatter
        
        try:
            start_time = time.time()

            # 🔥 **删除重复的阻塞检测逻辑**：last_data_time由enhanced_blocking_tracker统一处理
            # 避免与统一阻塞追踪器的功能重复

            # 🔥 Gate.io特有的symbol提取逻辑
            symbol = data.get("currency_pair") or data.get("s") or data.get("symbol", "")
            if not symbol and self.symbols:
                symbol = self.symbols[0]
            
            if not symbol:
                self._log_debug(f"⚠️ [GATE] 无法提取交易对，跳过")
                return

            # 🔥 记录数据接收（统一阻塞追踪）
            self._log_data_received("gate", self.market_type, symbol, data)
            
            # 🔥 **关键修复**：调用统一连接池管理器的数据流更新
            if self._integrated_with_pool and self.pool_manager:
                try:
                    self.pool_manager.update_symbol_data_flow(self.pool_connection_id, symbol)
                except Exception as e:
                    self.logger.debug(f"数据流更新失败: {e}")
            
            # 🔥 **删除重复的数据流监控**：enhanced_blocking_tracker已通过_log_data_received处理
            # 避免重复更新时间戳处理器状态

            # 🔥 Gate.io快照模式：使用简化锁机制（不需要维护状态）
            if symbol not in self.orderbook_locks:
                self.orderbook_locks[symbol] = asyncio.Lock()

            async with self.orderbook_locks[symbol]:
                # 🔥 使用统一时间戳处理器
                timestamp = self.timestamp_processor.get_synced_timestamp(data)

                # 🔥 **CRITICAL修复**：Gate期货数据格式解析错误 - 支持多种数据结构
                # 1. 检查是否数据嵌套在result字段中
                if "result" in data and isinstance(data["result"], dict):
                    orderbook_data = data["result"]
                    self._log_debug(f"[GATE] 使用嵌套result数据: {list(orderbook_data.keys())}")
                else:
                    orderbook_data = data
                    self._log_debug(f"[GATE] 使用直接数据: {list(orderbook_data.keys())}")
                
                # 2. 支持多种字段名称：'asks'/'bids' 或 'a'/'b'
                raw_asks = orderbook_data.get("asks", orderbook_data.get("a", []))
                raw_bids = orderbook_data.get("bids", orderbook_data.get("b", []))
                
                # 3. 调试日志：记录原始数据结构
                self._log_debug(f"[GATE] {symbol} 原始数据: asks={len(raw_asks)}, bids={len(raw_bids)}")
                if raw_asks and len(raw_asks) > 0:
                    self._log_debug(f"[GATE] {symbol} 第一个ask样本: {raw_asks[0]}")
                if raw_bids and len(raw_bids) > 0:
                    self._log_debug(f"[GATE] {symbol} 第一个bid样本: {raw_bids[0]}")

                # 🔥 Gate.io数据格式转换：[price, size] -> [float, float]
                formatted_asks = []
                formatted_bids = []

                # 🔥 **CRITICAL修复**：Gate期货数据格式解析错误
                # 问题：代码期望 [price, size] 数组，但Gate期货实际返回 {'p': price, 's': size} 字典
                # 修复：支持两种格式的数据解析
                
                # 处理asks - 支持数组和字典两种格式
                for ask in raw_asks[:30]:  # 取前30档
                    try:
                        if isinstance(ask, list) and len(ask) >= 2:
                            # 数组格式: [price, size] - 🔥 统一使用Decimal高精度处理
                            from decimal import Decimal
                            price = Decimal(str(ask[0]))
                            size = Decimal(str(ask[1]))
                        elif isinstance(ask, dict):
                            # 🔥 字典格式: {'p': price, 's': size} - Gate期货实际格式
                            from decimal import Decimal
                            price = Decimal(str(ask.get('p', ask.get('price', 0))))
                            size = Decimal(str(ask.get('s', ask.get('size', ask.get('amount', 0)))))
                        else:
                            continue
                            
                        if price > 0 and size > 0:
                            # 🔥 通用系统支持任意代币：统一输出格式，转换为float兼容下游
                            formatted_asks.append([float(price), float(size)])
                    except (ValueError, TypeError, KeyError) as e:
                        # 🔥 通用系统支持任意代币：具体化异常处理，提升调试能力
                        self._log_debug(f"Gate.io数据解析异常: {e}, 原始数据: {str(ask)[:100]}")
                        continue

                # 处理bids - 支持数组和字典两种格式
                for bid in raw_bids[:30]:  # 取前30档
                    try:
                        if isinstance(bid, list) and len(bid) >= 2:
                            # 数组格式: [price, size] - 🔥 统一使用Decimal高精度处理
                            from decimal import Decimal
                            price = Decimal(str(bid[0]))
                            size = Decimal(str(bid[1]))
                        elif isinstance(bid, dict):
                            # 🔥 字典格式: {'p': price, 's': size} - Gate期货实际格式
                            from decimal import Decimal
                            price = Decimal(str(bid.get('p', bid.get('price', 0))))
                            size = Decimal(str(bid.get('s', bid.get('size', bid.get('amount', 0)))))
                        else:
                            continue
                            
                        if price > 0 and size > 0:
                            # 🔥 通用系统支持任意代币：统一输出格式，转换为float兼容下游
                            formatted_bids.append([float(price), float(size)])
                    except (ValueError, TypeError, KeyError) as e:
                        # 🔥 通用系统支持任意代币：具体化异常处理，提升调试能力
                        self._log_debug(f"Gate.io数据解析异常: {e}, 原始数据: {str(ask)[:100]}")
                        continue

                # 🔥 Gate.io数据已排序，只需验证排序正确性
                if formatted_asks:
                    # 确保asks从低到高排序
                    if len(formatted_asks) > 1 and formatted_asks[0][0] > formatted_asks[1][0]:
                        formatted_asks = sorted(formatted_asks, key=lambda x: x[0])
                    formatted_asks = formatted_asks[:30]
                    
                if formatted_bids:
                    # 确保bids从高到低排序
                    if len(formatted_bids) > 1 and formatted_bids[0][0] < formatted_bids[1][0]:
                        formatted_bids = sorted(formatted_bids, key=lambda x: x[0], reverse=True)
                    formatted_bids = formatted_bids[:30]

                # 🔥 Gate.io快照模式要求：必须有买卖盘数据
                if not formatted_asks or not formatted_bids:
                    self._log_warning(f"⚠️ Gate快照数据不完整: {symbol} asks={len(formatted_asks)} bids={len(formatted_bids)}")
                    return

                # 🔥 使用统一验证器
                validator = get_orderbook_validator()
                orderbook_data = {'asks': formatted_asks, 'bids': formatted_bids}
                validation_result = validator.validate_orderbook_data(
                    orderbook_data, exchange="gate", symbol=symbol, market_type=self.market_type
                )

                if not validation_result.is_valid:
                    self._log_warning(f"⚠️ Gate订单簿验证失败: {validation_result.error_message}")
                    return

                # 🔥 使用统一数据格式化器
                standard_symbol = normalize_symbol(symbol)
                formatter = get_orderbook_formatter()
                formatted_orderbook = formatter.format_orderbook_data(
                    asks=formatted_asks,
                    bids=formatted_bids,
                    symbol=standard_symbol,
                    exchange="gate",
                    market_type=self.market_type,
                    timestamp=timestamp
                )

                # 🔥 记录性能指标
                record_message_latency(start_time)

                # 🔥 统一数据流发送
                self.emit("market_data", formatted_orderbook)

        except Exception as e:
            # 🔥 统一错误处理
            self._log_error(f"🚨 Gate订单簿处理失败: {str(e)}")
            raise

    async def _resubscribe_symbol(self, symbol: str):
        """
        🔥 重新订阅单个交易对的订单簿数据 - 容错机制
        当检测到订单簿数据为空时，尝试重新订阅
        """
        try:
            self._log_info(f"🔄 [GATE-{self.market_type.upper()}] 重新订阅 {symbol} 订单簿数据")

            # 等待一小段时间避免频繁重订阅
            await asyncio.sleep(1.0)

            # 重新订阅该交易对的订单簿（前10档）
            if self.market_type == "spot":
                depth_msg = {
                    "time": int(time.time()),
                    "channel": "spot.order_book",
                    "event": "subscribe",
                    "payload": [symbol, "50", "100ms"]  # 🔥 **官方API规范修复**: 3参数格式 [symbol, level, interval]
                }
            else:
                # 🔥 关键修复：期货订单簿只需要交易对名称，根据官方文档示例
                depth_msg = {
                    "time": int(time.time()),
                    "channel": "futures.order_book",
                    "event": "subscribe",
                    "payload": [symbol]  # 🔥 修复：期货只需要[symbol]格式，参考官方文档
                }

            success = await self.send(depth_msg)
            if success:
                self._log_info(f"✅ [GATE-{self.market_type.upper()}] {symbol} 重新订阅成功")
            else:
                self._log_error(f"❌ [GATE-{self.market_type.upper()}] {symbol} 重新订阅失败")

        except Exception as e:
            self._log_error(f"❌ [GATE-{self.market_type.upper()}] 重新订阅 {symbol} 时发生错误: {e}")
    
    async def _handle_trades(self, data: List[Dict[str, Any]]):
        """处理成交数据"""
        try:
            for trade in data:
                # 🔥 使用高精度Decimal处理交易数据
                from decimal import Decimal
                trade_data = {
                    "symbol": trade.get("currency_pair", trade.get("contract", "")),
                    "price": float(Decimal(str(trade.get("price", 0)))),  # 🔥 转换为float保持兼容性
                    "amount": float(Decimal(str(trade.get("amount", trade.get("size", 0))))),  # 🔥 转换为float保持兼容性
                    "side": trade.get("side", ""),
                    # 🔥 时间戳修复：优先使用服务器时间戳
                    "timestamp": trade.get("create_time_ms", trade.get("timestamp", trade.get("t", int(time.time() * 1000)))),
                    "exchange": "GATE",  # 使用统一的交易所名称
                    "market_type": self.market_type
                }
                
                self._log_debug(f"处理成交: {trade_data['symbol']} {trade_data['side']} {trade_data['amount']} @ {trade_data['price']}")
                self.emit("trade", trade_data)
                
        except Exception as e:
            self._log_error(f"处理成交数据错误: {e}", exc_info=True)
    
    async def send_heartbeat(self):
        """发送心跳"""
        ping_msg = {
            "time": int(time.time()),
            "channel": "spot.ping" if self.market_type == "spot" else "futures.ping"
        }
        self._log_debug(f"发送心跳: {json.dumps(ping_msg)}")
        success = await self.send(ping_msg)
        if success:
            self.last_message_time = time.time()  # 更新最后消息时间
        return success
    
    def get_status(self):
        """获取WebSocket状态"""
        connected = self.ws is not None and self.ws.open if hasattr(self, 'ws') else False
        return {
            "connected": connected,
            "reconnect_count": self.reconnect_count,
            "message_count": self.orderbook_count + self.trade_count,
            "last_message_time": self.last_message_time
        }


# 测试代码
if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 配置简单的日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # ticker处理已移除
    
    async def main():
        # 测试现货WebSocket
        spot_client = GateWebSocketClient("spot")
        # 使用动态配置的交易对
        from exchanges.currency_adapter import currency_adapter
        test_symbols = currency_adapter.get_supported_symbols()[:2] if currency_adapter.get_supported_symbols() else ["ADA_USDT", "SOL_USDT"]
        # 转换为Gate.io格式
        gate_symbols = [symbol.replace("-", "_") for symbol in test_symbols]
        
        spot_client.set_symbols(gate_symbols)
        # ticker回调已移除
        # spot_client.register_callback("orderbook", on_orderbook)  # 已移除
        
        # 测试期货WebSocket
        futures_client = GateWebSocketClient("futures")
        futures_client.set_symbols(gate_symbols)
        # ticker回调已移除
        # futures_client.register_callback("orderbook", on_orderbook)  # 已移除
        
        # 运行30秒后退出
        tasks = [
            asyncio.create_task(spot_client.run()),
            asyncio.create_task(futures_client.run())
        ]
        
        await asyncio.sleep(30)
        
        spot_client.running = False
        futures_client.running = False
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    asyncio.run(main())
