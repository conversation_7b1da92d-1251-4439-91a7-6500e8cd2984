"""
WebSocket模块
提供与各交易所WebSocket连接的功能
"""

import asyncio
import logging

def _emergency_symbol_convert(symbol: str, exchange: str, market_type: str) -> str:
    """🔥 **应急符号转换**：当currency_adapter不可用时的备用转换逻辑"""
    try:
        if exchange == "gate":
            # Gate格式: BTC_USDT
            return symbol.replace("-", "_")
        elif exchange == "bybit":
            # Bybit格式: BTCUSDT
            return symbol.replace("-", "")
        elif exchange == "okx":
            # OKX格式: BTC-USDT 或 BTC-USDT-SWAP
            if market_type == "futures":
                if "-SWAP" not in symbol:
                    return f"{symbol}-SWAP"
                else:
                    return symbol
            else:
                if "-SWAP" in symbol:
                    return symbol.replace("-SWAP", "")
                else:
                    return symbol
        else:
            # 默认保持原样
            return symbol
    except Exception as e:
        logging.error(f"应急符号转换失败: {e}")
        return symbol

# 🔥 修复：安全导入WebSocket客户端，避免循环依赖
try:
    from .ws_client import WebSocketClient
except ImportError as e:
    logging.warning(f"WebSocket基类导入失败: {e}")
    WebSocketClient = None

try:
    from .gate_ws import GateWebSocketClient
except ImportError as e:
    logging.warning(f"Gate WebSocket客户端导入失败: {e}")
    GateWebSocketClient = None

try:
    from .bybit_ws import BybitWebSocketClient
except ImportError as e:
    logging.warning(f"Bybit WebSocket客户端导入失败: {e}")
    BybitWebSocketClient = None

try:
    from .okx_ws import OKXWebSocketClient
except ImportError as e:
    logging.warning(f"OKX WebSocket客户端导入失败: {e}")
    OKXWebSocketClient = None

try:
    from .ws_manager import WebSocketManager, get_ws_manager, set_ws_manager
except ImportError as e:
    logging.warning(f"WebSocket管理器导入失败: {e}")
    WebSocketManager = None
    get_ws_manager = None
    set_ws_manager = None

# 🔥 统一模块导入
try:
    from .unified_data_formatter import get_orderbook_formatter, UnifiedOrderbookFormatter
except ImportError as e:
    logging.warning(f"统一数据格式化器导入失败: {e}")
    get_orderbook_formatter = None
    UnifiedOrderbookFormatter = None

try:
    from .orderbook_validator import get_orderbook_validator
except ImportError as e:
    logging.warning(f"订单簿验证器导入失败: {e}")
    get_orderbook_validator = None

try:
    from .unified_timestamp_processor import get_timestamp_processor
except ImportError as e:
    logging.warning(f"统一时间戳处理器导入失败: {e}")
    get_timestamp_processor = None

# 初始化日志
logger = logging.getLogger("websocket")

# 全局变量，用于存储运行中的WebSocket客户端
_running_clients = []


# 数据处理回调
def handle_market_data(data):
    """处理市场数据的统一回调函数"""
    exchange = data.get('exchange', 'UNKNOWN')
    symbol = data.get('symbol', 'UNKNOWN')
    market_type = data.get('market_type', 'UNKNOWN')
    
    if 'last' in data:  # Ticker数据
        logger.debug(f"收到Ticker: {exchange} {market_type} {symbol} - 最新价: {data.get('last', 0)}")
    elif 'asks' in data and 'bids' in data:  # 深度数据
        logger.debug(f"收到深度: {exchange} {market_type} {symbol} - 深度: {len(data.get('asks', []))}档")
    elif 'trades' in data:  # 成交数据
        logger.debug(f"收到成交: {exchange} {market_type} {symbol} - 数量: {len(data.get('trades', []))}")


async def start_gate_ws(symbols, market_types=None, callbacks=None):
    """启动Gate.io WebSocket客户端"""
    if market_types is None:
        market_types = ["spot", "futures"]
    
    clients = []
    for market_type in market_types:
        logger.info(f"启动Gate.io {market_type.upper()} WebSocket客户端")
        client = GateWebSocketClient(market_type)
        
        # 设置交易对
        client.set_symbols(symbols)
        
        # 注册回调
        if callbacks:
            for event, callback in callbacks.items():
                client.register_callback(event, callback)
        else:
            # 默认回调
            client.register_callback("ticker", handle_market_data)
            client.register_callback("orderbook", handle_market_data)
            client.register_callback("trade", handle_market_data)
        
        clients.append(client)
    
    return clients


async def start_bybit_ws(symbols, market_types=None, callbacks=None):
    """启动Bybit WebSocket客户端"""
    if market_types is None:
        market_types = ["spot", "futures"]
    
    clients = []
    for market_type in market_types:
        logger.info(f"启动Bybit {market_type.upper()} WebSocket客户端")
        client = BybitWebSocketClient(market_type)
        
        # 设置交易对
        client.set_symbols(symbols)
        
        # 注册回调
        if callbacks:
            for event, callback in callbacks.items():
                client.register_callback(event, callback)
        else:
            # 默认回调
            client.register_callback("ticker", handle_market_data)
            client.register_callback("orderbook", handle_market_data)
            client.register_callback("trade", handle_market_data)
        
        clients.append(client)
    
    return clients


async def start_okx_ws(symbols, market_types=None, callbacks=None):
    """启动OKX WebSocket客户端"""
    if market_types is None:
        market_types = ["spot", "futures"]
    
    clients = []
    for market_type in market_types:
        logger.info(f"启动OKX {market_type.upper()} WebSocket客户端")
        client = OKXWebSocketClient("OKX", market_type)
        
        # 设置交易对
        if market_type == "futures":
            # OKX期货交易对格式需要特殊处理
            futures_symbols = [f"{s}-SWAP" if not s.endswith("-SWAP") else s for s in symbols]
            client.set_symbols(futures_symbols)
        else:
            client.set_symbols(symbols)
        
        # 注册回调
        if callbacks:
            for event, callback in callbacks.items():
                client.register_callback(event, callback)
        else:
            # 默认回调
            client.register_callback("ticker", handle_market_data)
            client.register_callback("orderbook", handle_market_data)
            client.register_callback("trade", handle_market_data)
        
        clients.append(client)
    
    return clients


async def start_websockets(symbols, exchanges=None, market_types=None, callbacks=None, exchange_symbols=None):
    """启动所有交易所WebSocket客户端 - 🔥 完全重写：支持多组合运行机制
    
    Args:
        symbols: 标准交易对列表，如 ["BTC-USDT", "ETH-USDT"]
        exchanges: 要启动的交易所列表，如 ["gate", "bybit", "okx"]
        market_types: 市场类型列表，如 ["spot", "futures"]
        callbacks: 回调函数字典，格式为 {'event_name': callback_function}
        exchange_symbols: 交易所特定格式的交易对字典（可选，系统会自动转换）
    
    Returns:
        asyncio.Task: 一个代表所有WebSocket客户端的任务
    """
    global _running_clients
    
    if exchanges is None:
        exchanges = ["gate", "bybit", "okx"]
    
    if market_types is None:
        market_types = ["spot", "futures"]
    
    logger.info(f"🚀 启动多组合WebSocket系统：")
    logger.info(f"   📊 交易所: {', '.join(exchanges)}")
    logger.info(f"   💹 市场类型: {', '.join(market_types)}")
    logger.info(f"   🎯 交易对: {', '.join(symbols[:3])}...共{len(symbols)}个")
    
    # 🔥 关键修复：为每个交易所+市场类型创建独立的WebSocket客户端
    clients = []
    client_info = []  # 记录客户端信息用于调试
    
    for exchange in exchanges:
        for market_type in market_types:
            try:
                # 🎯 为每个交易所的每个市场类型创建独立客户端
                client_key = f"{exchange}_{market_type}"
                
                # 🔥 **安全的通用化修复**：使用统一currency_adapter转换symbol格式
                converted_symbols = []
                try:
                    from exchanges.currency_adapter import get_exchange_symbol
                    use_unified_adapter = True
                except ImportError:
                    logger.warning("currency_adapter不可用，使用应急转换逻辑")
                    use_unified_adapter = False

                for symbol in symbols:
                    if use_unified_adapter:
                        try:
                            # 使用统一的符号转换器，移除交易所特定处理逻辑
                            converted_symbol = get_exchange_symbol(symbol, exchange, market_type)
                            converted_symbols.append(converted_symbol)
                        except Exception as e:
                            logger.warning(f"转换{symbol}到{exchange}格式失败: {e}，使用应急转换")
                            # 应急转换逻辑
                            converted_symbols.append(_emergency_symbol_convert(symbol, exchange, market_type))
                    else:
                        # 应急转换逻辑
                        converted_symbols.append(_emergency_symbol_convert(symbol, exchange, market_type))
                
                # 🔥 创建对应的WebSocket客户端
                if exchange == "gate":
                    client = GateWebSocketClient(market_type)
                elif exchange == "bybit":
                    client = BybitWebSocketClient(market_type)
                elif exchange == "okx":
                    client = OKXWebSocketClient("OKX", market_type)
                else:
                    logger.warning(f"⚠️ 不支持的交易所: {exchange}")
                    continue
                
                # 🎯 设置交易对
                client.set_symbols(converted_symbols)
                
                # 🔥 注册回调函数 - 确保数据正确传递
                if callbacks:
                    for event, callback in callbacks.items():
                        client.register_callback(event, callback)
                        
                    # 🚨 关键修复：确保market_data回调存在 - 修复OKX差价发现问题
                    if "market_data" not in callbacks:
                        # 如果没有专门的market_data回调，使用ticker回调作为默认
                        if "ticker" in callbacks:
                            client.register_callback("market_data", callbacks["ticker"])
                        else:
                            # 如果连ticker回调都没有，使用默认的handle_market_data
                            client.register_callback("market_data", handle_market_data)
                else:
                    # 默认回调
                    client.register_callback("ticker", handle_market_data)
                    client.register_callback("market_data", handle_market_data)
                    client.register_callback("orderbook", handle_market_data)
                    client.register_callback("trade", handle_market_data)
                
                # 🔥 给客户端添加标识，便于调试
                client._client_key = client_key
                client._exchange = exchange
                client._market_type = market_type
                client._converted_symbols = converted_symbols
                
                clients.append(client)
                client_info.append(f"✅ {client_key}: {len(converted_symbols)}个交易对")
                
                logger.info(f"✅ 创建{client_key}客户端: {converted_symbols[:2]}...共{len(converted_symbols)}个")
                
            except Exception as e:
                logger.error(f"❌ 创建{exchange}_{market_type}客户端失败: {e}")
                client_info.append(f"❌ {exchange}_{market_type}: 失败")
    
    # 🔥 保存到全局变量
    _running_clients = clients
    
    if not clients:
        logger.error("🚨 所有WebSocket客户端创建失败！")
        raise Exception("无法创建任何WebSocket客户端")
    
    # 🎯 状态汇总
    logger.info(f"🔄 WebSocket客户端创建完成:")
    for info in client_info:
        logger.info(f"   {info}")
    
    logger.info(f"🎉 多组合WebSocket系统准备就绪: {len(clients)}个客户端")
    
    # 🔥 启动所有客户端 - 异步并行启动
    logger.info("🚀 启动所有WebSocket连接...")
    
    async def start_single_client(client):
        """启动单个WebSocket客户端"""
        try:
            client_key = getattr(client, '_client_key', 'unknown')
            logger.info(f"🔌 启动{client_key}...")
            
            # 🔥 关键修复：使用正确的启动方法（与老版本一致）
            await client.start_connection()
            
        except Exception as e:
            client_key = getattr(client, '_client_key', 'unknown')
            logger.error(f"❌ {client_key}运行异常: {e}")
    
    # 🎯 创建所有客户端的并行任务
    tasks = [asyncio.create_task(start_single_client(client)) for client in clients]
    
    # 🔥 等待所有客户端建立连接（最多等待5秒）
    try:
        logger.info("⏳ 等待WebSocket连接建立...")
        await asyncio.sleep(2)  # 给客户端一些时间建立连接
        
        # 🎯 检查连接状态
        connected_count = 0
        for client in clients:
            client_key = getattr(client, '_client_key', 'unknown')
            try:
                if hasattr(client, 'is_connected') and callable(client.is_connected):
                    if client.is_connected():
                        connected_count += 1
                        logger.info(f"✅ {client_key}已连接")
                    else:
                        logger.warning(f"⚠️ {client_key}连接中...")
                else:
                    # 假设连接成功
                    connected_count += 1
                    logger.info(f"🔄 {client_key}状态未知（假设已连接）")
            except Exception as e:
                logger.warning(f"⚠️ 检查{client_key}状态失败: {e}")
        
        logger.info(f"🎯 WebSocket连接状态: {connected_count}/{len(clients)}个已连接")
        
        if connected_count == 0:
            logger.warning("⚠️ 没有WebSocket客户端报告连接成功，但系统将继续运行")
        
    except Exception as e:
        logger.error(f"❌ 检查WebSocket连接状态失败: {e}")
    
    # 🔥 返回一个包装所有任务的主任务
    async def monitor_all_clients():
        """监控所有WebSocket客户端"""
        try:
            logger.info("📡 WebSocket监控系统启动")
            # 等待所有任务完成（实际上是无限运行）
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"❌ WebSocket监控系统异常: {e}")
    
    # 创建监控任务
    monitor_task = asyncio.create_task(monitor_all_clients())
    
    logger.info(f"🎉 多组合WebSocket系统全面启动: {len(clients)}个客户端正在运行")
    
    return monitor_task


def stop_all_websockets():
    """停止所有运行中的WebSocket客户端"""
    global _running_clients
    
    logger.info(f"停止 {len(_running_clients)} 个WebSocket客户端连接")
    
    for client in _running_clients:
        client.running = False
    
    logger.info("已发送停止信号到所有WebSocket客户端")


def get_running_clients():
    """获取当前运行中的所有WebSocket客户端"""
    return _running_clients


# 🔥 修复：动态导出列表，只导出成功导入的类
__all__ = ['start_websockets', 'stop_all_websockets', 'get_running_clients', 'handle_market_data']

# 动态添加成功导入的客户端类
if WebSocketClient is not None:
    __all__.append('WebSocketClient')
if GateWebSocketClient is not None:
    __all__.append('GateWebSocketClient')
if BybitWebSocketClient is not None:
    __all__.append('BybitWebSocketClient')
if OKXWebSocketClient is not None:
    __all__.append('OKXWebSocketClient')
if WebSocketManager is not None:
    __all__.extend(['WebSocketManager', 'get_ws_manager', 'set_ws_manager'])

# 🔥 添加统一模块到导出列表
if get_orderbook_formatter is not None:
    __all__.extend(['get_orderbook_formatter', 'UnifiedOrderbookFormatter'])
if get_orderbook_validator is not None:
    __all__.append('get_orderbook_validator')
if get_timestamp_processor is not None:
    __all__.append('get_timestamp_processor')