# -*- coding: utf-8 -*-
"""
🔥 统一订单簿数据验证器
消除ExecutionEngine和WsManager中的重复验证逻辑
确保接口一致性和参数统一
"""

import os
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class OrderbookValidationConfig:
    """🔥 升级配置：统一30档深度分析"""
    max_depth_levels: int = 30    # 🔥 升级：前30档分析，与UnifiedOrderSpreadCalculator保持一致


@dataclass
class OrderbookValidationResult:
    """订单簿验证结果 - 🔥 简化版本：只关心是否可用"""
    is_valid: bool                         # 是否通过验证
    error_message: str = ""                 # 错误信息
    asks_count: int = 0                     # asks档位数量
    bids_count: int = 0                     # bids档位数量


class UnifiedOrderbookValidator:
    """
    🔥 统一订单簿数据验证器
    替代ExecutionEngine和WsManager中的重复验证逻辑
    """
    
    def __init__(self, config: Optional[OrderbookValidationConfig] = None):
        self.config = config or OrderbookValidationConfig()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def validate_orderbook_data(
        self, 
        orderbook: Dict[str, Any], 
        exchange: str = "", 
        symbol: str = "",
        market_type: str = "spot"
    ) -> OrderbookValidationResult:
        """
        🔥 统一订单簿数据验证接口
        
        Args:
            orderbook: 订单簿数据
            exchange: 交易所名称
            symbol: 交易对
            market_type: 市场类型
            
        Returns:
            OrderbookValidationResult: 验证结果
        """
        try:
            if not orderbook:
                return OrderbookValidationResult(
                    is_valid=False,
                    error_message="订单簿数据为空"
                )
            
            # 1. 基础字段验证
            asks = orderbook.get('asks', [])
            bids = orderbook.get('bids', [])
            # 🔥 删除时间戳获取
            
            # 2. 数据完整性检查
            asks_count = len(asks) if asks else 0
            bids_count = len(bids) if bids else 0
            
            # 🔥 简化：删除时间戳验证
            
            # 🔥 简化：删除数据新鲜度检查，实时分析不需要
            
            # 🔥 智能处理：检查数据完整性，支持动态订单簿
            if asks_count == 0 and bids_count == 0:
                # 🔥 检查是否是ticker数据（只有价格没有订单簿）
                if 'price' in orderbook or 'last' in orderbook or 'lastPrice' in orderbook:
                    self.logger.debug(f"📊 检测到ticker数据（无订单簿）: {exchange}_{market_type}_{symbol}")
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message="ticker数据，无订单簿信息",
                        asks_count=0,
                        bids_count=0
                    )
                else:
                    # 🔥 对于动态订单簿，这可能是正常的瞬时状态
                    self.logger.debug(f"📊 订单簿暂时为空（动态变化）: {exchange}_{market_type}_{symbol}")
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message="订单簿数据完全为空",
                        asks_count=0,
                        bids_count=0
                    )

            # 🔥 智能处理：单边数据（只有买单或只有卖单）也是有效的
            if asks_count == 0 or bids_count == 0:
                self.logger.debug(f"📊 订单簿单边数据: {exchange}_{market_type}_{symbol} - asks={asks_count}, bids={bids_count}")
                # 单边数据仍然可以用于价格分析，不返回失败

            # 🔥 基本格式验证：确保数据可用
            format_result = self._validate_orderbook_format(asks, bids)
            if not format_result.is_valid:
                return format_result

            # 🔥 简化：删除质量评分计算，只记录基本信息
            self.logger.debug(f"📊 订单簿数据: asks={asks_count}, bids={bids_count}")

            # 验证通过
            return OrderbookValidationResult(
                is_valid=True,
                asks_count=asks_count,
                bids_count=bids_count
            )
            
        except Exception as e:
            self.logger.error(f"订单簿验证异常: {e}")
            return OrderbookValidationResult(
                is_valid=False,
                error_message=f"验证异常: {str(e)}"
            )
    
    def _validate_orderbook_format(self, asks: List, bids: List) -> OrderbookValidationResult:
        """验证订单簿数据格式"""
        try:
            # 检查asks格式
            for i, ask in enumerate(asks[:self.config.max_depth_levels]):
                if not isinstance(ask, (list, tuple)) or len(ask) < 2:
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message=f"asks格式错误: 第{i}档 {ask}"
                    )
                
                try:
                    price, volume = float(ask[0]), float(ask[1])
                    if price <= 0 or volume < 0:
                        return OrderbookValidationResult(
                            is_valid=False,
                            error_message=f"asks数据无效: 价格={price}, 数量={volume}"
                        )
                except (ValueError, TypeError):
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message=f"asks数据类型错误: {ask}"
                    )
            
            # 检查bids格式
            for i, bid in enumerate(bids[:self.config.max_depth_levels]):
                if not isinstance(bid, (list, tuple)) or len(bid) < 2:
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message=f"bids格式错误: 第{i}档 {bid}"
                    )
                
                try:
                    price, volume = float(bid[0]), float(bid[1])
                    if price <= 0 or volume < 0:
                        return OrderbookValidationResult(
                            is_valid=False,
                            error_message=f"bids数据无效: 价格={price}, 数量={volume}"
                        )
                except (ValueError, TypeError):
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message=f"bids数据类型错误: {bid}"
                    )
            
            return OrderbookValidationResult(is_valid=True)
            
        except Exception as e:
            return OrderbookValidationResult(
                is_valid=False,
                error_message=f"格式验证异常: {str(e)}"
            )
    
    def _validate_price_reasonableness(self, asks: List, bids: List) -> OrderbookValidationResult:
        """验证价格合理性 - 🔥 修复：智能处理部分数据，避免价格倒挂误报"""
        try:
            # 🔥 关键修复：允许部分数据，只验证存在的数据
            if not asks and not bids:
                return OrderbookValidationResult(is_valid=True)  # 完全空数据跳过价格检查

            # 🔥 部分数据处理：只有一边有数据时，只验证该边的数据
            if not asks and bids:
                # 只有买单，验证买单价格合理性
                sorted_bids = sorted(bids, key=lambda x: float(x[0]) if len(x) >= 1 else 0, reverse=True)
                best_bid = float(sorted_bids[0][0]) if sorted_bids and len(sorted_bids[0]) >= 1 else 0
                if best_bid <= 0:
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message=f"买单价格无效: best_bid={best_bid}"
                    )
                return OrderbookValidationResult(is_valid=True)  # 只有买单，价格合理

            if asks and not bids:
                # 只有卖单，验证卖单价格合理性
                sorted_asks = sorted(asks, key=lambda x: float(x[0]) if len(x) >= 1 else 0)
                best_ask = float(sorted_asks[0][0]) if sorted_asks and len(sorted_asks[0]) >= 1 else 0
                if best_ask <= 0:
                    return OrderbookValidationResult(
                        is_valid=False,
                        error_message=f"卖单价格无效: best_ask={best_ask}"
                    )
                return OrderbookValidationResult(is_valid=True)  # 只有卖单，价格合理

            # 🔥 双边数据处理：先排序再验证，确保数据正确性
            # asks按价格升序排列（最低卖价在前）
            sorted_asks = sorted(asks, key=lambda x: float(x[0]) if len(x) >= 1 else 0)
            # bids按价格降序排列（最高买价在前）
            sorted_bids = sorted(bids, key=lambda x: float(x[0]) if len(x) >= 1 else 0, reverse=True)

            # 获取排序后的最优价格
            best_ask = float(sorted_asks[0][0]) if sorted_asks and len(sorted_asks[0]) >= 1 else 0
            best_bid = float(sorted_bids[0][0]) if sorted_bids and len(sorted_bids[0]) >= 1 else 0

            if best_ask <= 0 or best_bid <= 0:
                return OrderbookValidationResult(
                    is_valid=False,
                    error_message=f"最优价格无效: best_ask={best_ask}, best_bid={best_bid}"
                )

            # 检查买卖价差合理性（排序后应该不会出现倒挂）
            if best_ask <= best_bid:
                return OrderbookValidationResult(
                    is_valid=False,
                    error_message=f"价格倒挂: best_ask={best_ask} <= best_bid={best_bid} (已排序后仍倒挂，数据异常)"
                )

            # 检查价差是否过大
            spread_ratio = (best_ask - best_bid) / best_bid
            if spread_ratio > self.config.price_deviation_threshold:
                return OrderbookValidationResult(
                    is_valid=False,
                    error_message=f"价差过大: {spread_ratio:.4f} > {self.config.price_deviation_threshold}"
                )

            return OrderbookValidationResult(is_valid=True)

        except Exception as e:
            return OrderbookValidationResult(
                is_valid=False,
                error_message=f"价格验证异常: {str(e)}"
            )

    # 🔥 删除质量评分计算方法


# 🌟 全局实例
_global_validator = None

def get_orderbook_validator(config: Optional[OrderbookValidationConfig] = None) -> UnifiedOrderbookValidator:
    """获取全局订单簿验证器实例"""
    global _global_validator
    if _global_validator is None or config is not None:
        _global_validator = UnifiedOrderbookValidator(config)
    return _global_validator


def validate_orderbook_data(
    orderbook: Dict[str, Any],
    exchange: str = "",
    symbol: str = "",
    market_type: str = "spot",
    config: Optional[OrderbookValidationConfig] = None
) -> OrderbookValidationResult:
    """快速验证订单簿数据"""
    validator = get_orderbook_validator(config)
    return validator.validate_orderbook_data(orderbook, exchange, symbol, market_type)


def validate_orderbook_synchronization(
    spot_orderbook: Dict[str, Any],
    futures_orderbook: Dict[str, Any],
    max_time_diff_ms: int = 1000,  # 🔥 **统一阈值修复**：统一为1000ms，与network_config.py一致
    adaptive_threshold: bool = True  # 🔥 新增：自适应阈值调整
) -> Tuple[bool, str]:
    """
    🔥 验证现货和期货订单簿数据同步性 - 增强版
    支持自适应阈值调整和智能验证策略

    Args:
        spot_orderbook: 现货订单簿数据
        futures_orderbook: 期货订单簿数据
        max_time_diff_ms: 最大时间差容忍度(毫秒)
        adaptive_threshold: 是否启用自适应阈值调整

    Returns:
        Tuple[bool, str]: (是否同步, 错误信息)
    """
    try:
        current_time = time.time() * 1000

        # 获取时间戳
        spot_timestamp = spot_orderbook.get('timestamp', current_time)
        futures_timestamp = futures_orderbook.get('timestamp', current_time)

        # 🔥 **关键修复**：使用改进的时间戳格式标准化，避免1e12判断错误
        spot_timestamp = _normalize_timestamp_format(spot_timestamp)
        futures_timestamp = _normalize_timestamp_format(futures_timestamp)

        # 检查时间戳对齐
        time_diff_ms = abs(spot_timestamp - futures_timestamp)

        # 🔥 **修复2**：优化订单簿同步验证 - 智能处理极端时间差（如143552.0ms）
        # 对超过1分钟的异常差异进行特殊处理
        if time_diff_ms > 60000:  # 超过1分钟的极端时间差
            logger.warning(
                f"⚠️ 检测到极端订单簿时间差: {time_diff_ms:.1f}ms，启用智能修正机制"
            )
            # 智能修正：使用较新的时间戳，重新计算合理时间差
            corrected_timestamp = max(spot_timestamp, futures_timestamp)
            current_check_time = int(time.time() * 1000)
            
            # 如果修正后时间戳合理（不超过当前时间5秒）
            if abs(corrected_timestamp - current_check_time) <= 5000:
                logger.info(f"🔧 应用智能时间戳修正，使用时间戳: {corrected_timestamp}")
                # 重新计算修正后的时间差（估算网络延迟100ms）
                time_diff_ms = 100
            else:
                logger.warning(f"⚠️ 智能修正失败，时间戳不合理: {corrected_timestamp}")
                # 使用当前时间作为兜底
                time_diff_ms = abs(current_check_time - min(spot_timestamp, futures_timestamp))

        # 🔥 修复：自适应阈值调整 - 确保不会降低基础阈值
        effective_threshold = max_time_diff_ms
        if adaptive_threshold:
            # 根据数据质量动态调整阈值
            spot_quality = _assess_orderbook_quality(spot_orderbook)
            futures_quality = _assess_orderbook_quality(futures_orderbook)

            # 🔥 **统一阈值修复**：从配置读取阈值，与network_config.py保持一致
            max_relaxed_threshold = int(os.getenv('ORDERBOOK_TIMEOUT', '1000'))  # 统一使用1000ms

            # 🔥 修复：自适应阈值只能放宽，不能收紧
            # 如果数据质量高，可以适当放宽时间要求
            if spot_quality >= 0.8 and futures_quality >= 0.8:
                effective_threshold = max(max_time_diff_ms, min(max_time_diff_ms * 1.5, max_relaxed_threshold))
            elif spot_quality >= 0.6 and futures_quality >= 0.6:
                effective_threshold = max(max_time_diff_ms, min(max_time_diff_ms * 1.2, max_relaxed_threshold))
            # 🔥 修复：数据质量低时，保持原始阈值，不降低
            else:
                effective_threshold = max_time_diff_ms

        if time_diff_ms > effective_threshold:
            # 🔥 **智能处理143552ms等极端时间差**：检测异常数据源问题
            if time_diff_ms > 60000:  # 超过1分钟的极端异常
                return False, f"时间戳异常: 时间差{time_diff_ms:.1f}ms > 60000ms，可能存在数据源问题或WebSocket静默断流"
            else:
                return False, f"订单簿数据非同步: 时间差{time_diff_ms:.1f}ms > {effective_threshold:.0f}ms"

        # 🔥 优化：数据新鲜度检查 - 根据网络状况动态调整
        data_age_spot = current_time - spot_timestamp
        data_age_futures = current_time - futures_timestamp

        # 🔥 新增：智能数据年龄阈值
        max_data_age = _calculate_adaptive_data_age_threshold(spot_orderbook, futures_orderbook)

        if data_age_spot > max_data_age or data_age_futures > max_data_age:
            return False, f"订单簿数据过期: spot={data_age_spot:.1f}ms, futures={data_age_futures:.1f}ms (阈值{max_data_age:.0f}ms)"

        return True, ""

    except Exception as e:
        return False, f"同步性验证异常: {str(e)}"

def _assess_orderbook_quality(orderbook: Dict[str, Any]) -> float:
    """🔥 新增：评估订单簿数据质量"""
    try:
        quality_score = 0.0

        # 检查asks和bids数据
        asks = orderbook.get('asks', [])
        bids = orderbook.get('bids', [])

        # 数据完整性 (40%)
        if asks and bids:
            quality_score += 0.4
        elif asks or bids:
            quality_score += 0.2

        # 数据深度 (30%)
        total_levels = len(asks) + len(bids)
        if total_levels >= 20:
            quality_score += 0.3
        elif total_levels >= 10:
            quality_score += 0.2
        elif total_levels >= 5:
            quality_score += 0.1

        # 价格合理性 (30%)
        if asks and bids:
            try:
                best_ask = float(asks[0][0]) if asks[0] else 0
                best_bid = float(bids[0][0]) if bids[0] else 0
                if best_ask > best_bid > 0:  # 正常的买卖价差
                    spread_ratio = (best_ask - best_bid) / best_bid
                    if spread_ratio < 0.01:  # 价差小于1%
                        quality_score += 0.3
                    elif spread_ratio < 0.05:  # 价差小于5%
                        quality_score += 0.2
                    else:
                        quality_score += 0.1
            except (IndexError, ValueError, TypeError):
                pass

        return min(quality_score, 1.0)

    except Exception:
        return 0.0

def _normalize_timestamp_format(timestamp: float) -> int:
    """
    🔥 **新增**：标准化时间戳格式，修复1e12判断逻辑错误

    Args:
        timestamp: 原始时间戳

    Returns:
        int: 标准化的毫秒时间戳
    """
    try:
        # 🔥 **关键修复**：更精确的时间戳格式判断
        # 避免1e12边界判断错误导致79373.0ms这样的时间差

        # 如果时间戳小于1e10（100亿），认为是秒级时间戳
        if timestamp < 1e10:
            return int(timestamp * 1000)
        # 如果时间戳在1e10到1e13之间，认为是毫秒级时间戳
        elif timestamp < 1e13:
            return int(timestamp)
        # 如果时间戳大于1e13，可能是微秒或纳秒级，需要转换
        elif timestamp < 1e16:
            return int(timestamp / 1000)  # 微秒转毫秒
        else:
            return int(timestamp / 1000000)  # 纳秒转毫秒

    except Exception:
        # 异常时返回当前时间
        return int(time.time() * 1000)

def _calculate_adaptive_data_age_threshold(spot_orderbook: Dict[str, Any], futures_orderbook: Dict[str, Any]) -> float:
    """🔥 新增：计算自适应数据年龄阈值"""
    try:
        base_threshold = 1000  # 基础阈值1000ms

        # 根据数据质量调整
        spot_quality = _assess_orderbook_quality(spot_orderbook)
        futures_quality = _assess_orderbook_quality(futures_orderbook)
        avg_quality = (spot_quality + futures_quality) / 2

        # 高质量数据可以容忍更长的年龄
        if avg_quality >= 0.8:
            return base_threshold * 1.5  # 1500ms
        elif avg_quality >= 0.6:
            return base_threshold * 1.2  # 1200ms
        else:
            return base_threshold  # 1000ms

    except Exception:
        return 1000  # 默认阈值


def validate_orderbook_completeness(
    orderbook: Dict[str, Any],
    min_asks: int = 0,  # 🔥 遵循MD文档：不要求最小档位
    min_bids: int = 0,  # 🔥 遵循MD文档：不要求最小档位
    allow_partial: bool = True  # 🔥 遵循MD文档：允许任何数据
) -> Tuple[bool, str, Dict[str, int]]:
    """
    🔥 验证订单簿数据完整性 - 遵循MD文档"前10档实时分析"设计
    替代WsManager中的重复完整性检查

    Args:
        orderbook: 订单簿数据
        min_asks: 最小asks档位要求（通常为0，遵循MD文档设计）
        min_bids: 最小bids档位要求（通常为0，遵循MD文档设计）
        allow_partial: 是否允许部分数据（默认True，遵循MD文档设计）

    Returns:
        Tuple[bool, str, Dict]: (是否可用, 状态信息, 统计信息)
    """
    try:
        if not orderbook:
            return False, "订单簿数据为空", {"asks": 0, "bids": 0}

        asks = orderbook.get('asks', [])
        bids = orderbook.get('bids', [])

        asks_count = len(asks) if asks else 0
        bids_count = len(bids) if bids else 0

        stats = {"asks": asks_count, "bids": bids_count}

        # 🔥 遵循MD文档设计：只检查是否完全没有数据
        if asks_count == 0 and bids_count == 0:
            return False, "订单簿数据完全为空", stats

        # 🔥 MD文档设计：任何数据都可以用于"前10档实时分析"
        # 不再检查最小档位要求，直接返回可用状态

        if asks_count == 0 and bids_count > 0:
            # 只有买单 - 可用于分析
            return True, f"部分数据（仅买单）: bids={bids_count}档", stats

        if bids_count == 0 and asks_count > 0:
            # 只有卖单 - 可用于分析
            return True, f"部分数据（仅卖单）: asks={asks_count}档", stats

        # 双边数据 - 最佳情况
        return True, f"完整数据: asks={asks_count}档, bids={bids_count}档", stats

        # 检查数据有效性
        for i, ask in enumerate(asks[:10]):
            if not isinstance(ask, (list, tuple)) or len(ask) < 2:
                return False, f"asks第{i}档格式错误: {ask}", stats
            try:
                price, volume = float(ask[0]), float(ask[1])
                if price <= 0:
                    return False, f"asks第{i}档价格无效: {price}", stats
            except (ValueError, TypeError):
                return False, f"asks第{i}档数据类型错误: {ask}", stats

        for i, bid in enumerate(bids[:10]):
            if not isinstance(bid, (list, tuple)) or len(bid) < 2:
                return False, f"bids第{i}档格式错误: {bid}", stats
            try:
                price, volume = float(bid[0]), float(bid[1])
                if price <= 0:
                    return False, f"bids第{i}档价格无效: {price}", stats
            except (ValueError, TypeError):
                return False, f"bids第{i}档数据类型错误: {bid}", stats

        return True, "", stats

    except Exception as e:
        return False, f"完整性验证异常: {str(e)}", {"asks": 0, "bids": 0}
