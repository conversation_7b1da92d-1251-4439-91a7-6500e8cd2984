{"timestamp": "2025-08-05T12:57:56.992942", "duplicate_count": 32, "issues": [{"type": "重复阻塞检测逻辑", "severity": "HIGH", "pattern": "data_staleness", "count": 3, "locations": [{"file": "websocket/unified_timestamp_processor.py", "line": 491, "context": "task = asyncio.create_task(pool_manager.handle_connection_issue(connection_id, \"data_staleness_detected\"))", "match": "data_staleness"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 281, "context": "if event_type == \"data_staleness_detected\" and details:", "match": "data_staleness"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 291, "context": "blocking_type=\"data_staleness\",", "match": "data_staleness"}]}, {"type": "重复阻塞检测逻辑", "severity": "HIGH", "pattern": "data_flow_blocking", "count": 8, "locations": [{"file": "websocket/enhanced_blocking_tracker.py", "line": 4, "context": "WebSocket数据流阻塞详细日志追踪系统", "match": "数据流阻塞"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 19, "context": "\"\"\"数据流阻塞事件\"\"\"", "match": "数据流阻塞"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 44, "context": "\"\"\"WebSocket数据流阻塞追踪器\"\"\"", "match": "数据流阻塞"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 68, "context": "self.blocking_logger.info(\"🔍 WebSocket数据流阻塞追踪器已启动\")", "match": "数据流阻塞"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 170, "context": "\"\"\"处理检测到的数据流阻塞\"\"\"", "match": "数据流阻塞"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 216, "context": "\"\"\"分析数据流阻塞的可能原因\"\"\"", "match": "数据流阻塞"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 418, "context": "\"\"\"获取数据流阻塞追踪器单例\"\"\"", "match": "数据流阻塞"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 475, "context": "print(\"⏳ 模拟数据流阻塞...\")", "match": "数据流阻塞"}]}, {"type": "重复阻塞检测逻辑", "severity": "HIGH", "pattern": "last_data_time", "count": 12, "locations": [{"file": "websocket/gate_ws.py", "line": 397, "context": "self.last_data_time = time.time()", "match": "last_data_time"}, {"file": "websocket/bybit_ws.py", "line": 341, "context": "self.last_data_time = time.time()", "match": "last_data_time"}, {"file": "websocket/okx_ws.py", "line": 65, "context": "self.last_data_time = 0", "match": "last_data_time"}, {"file": "websocket/okx_ws.py", "line": 349, "context": "self.last_data_time = time.time()", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 35, "context": "last_data_time: float", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 135, "context": "last_data_time=current_time,", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 147, "context": "if metrics.last_data_time > 0:", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 148, "context": "gap = current_time - metrics.last_data_time", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 154, "context": "metrics.last_data_time = current_time", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 191, "context": "start_time=self.exchange_metrics[f\"{exchange}_{market_type}\"].last_data_time,", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 368, "context": "recent_gap = current_time - metrics.last_data_time", "match": "last_data_time"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 393, "context": "gap = current_time - metrics.last_data_time", "match": "last_data_time"}]}, {"type": "重复阻塞检测逻辑", "severity": "HIGH", "pattern": "websocket_blocking", "count": 6, "locations": [{"file": "websocket/enhanced_blocking_tracker.py", "line": 43, "context": "class WebSocketBlockingTracker:", "match": "WebSocketBlocking"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 68, "context": "self.blocking_logger.info(\"🔍 WebSocket数据流阻塞追踪器已启动\")", "match": "blocking_logger.info(\"🔍 WebSocket"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 72, "context": "logger = logging.getLogger(\"websocket_blocking_tracker\")", "match": "websocket_blocking"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 80, "context": "blocking_file = self.log_dir / f\"websocket_blocking_{datetime.now().strftime('%Y%m%d')}.log\"", "match": "blocking_file = self.log_dir / f\"websocket"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 417, "context": "def get_blocking_tracker() -> WebSocketBlockingTracker:", "match": "blocking_tracker() -> WebSocket"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 421, "context": "_blocking_tracker_instance = WebSocketBlockingTracker()", "match": "blocking_tracker_instance = WebSocket"}]}, {"type": "重复阻塞检测逻辑", "severity": "HIGH", "pattern": "connection_timeout", "count": 6, "locations": [{"file": "websocket/gate_ws.py", "line": 95, "context": "self.connection_timeout = 10  # 🔥 统一10秒连接超时，与network_config.py一致", "match": "connection_timeout"}, {"file": "websocket/bybit_ws.py", "line": 117, "context": "self.connection_timeout = 10  # 🔥 修复：连接超时统一为10秒，与Gate.io、OKX保持一致", "match": "connection_timeout"}, {"file": "websocket/ws_client.py", "line": 60, "context": "self.connection_timeout = getattr(settings.system, 'ws_connect_timeout', 10)", "match": "connection_timeout = getattr(settings.system, 'ws_connect_timeout"}, {"file": "websocket/ws_client.py", "line": 73, "context": "self.connection_timeout = 10", "match": "connection_timeout"}, {"file": "websocket/ws_client.py", "line": 295, "context": "timeout=self.connection_timeout", "match": "timeout=self.connection"}, {"file": "websocket/ws_client.py", "line": 354, "context": "self._log_error(f\"连接超时，超过{self.connection_timeout}秒\")", "match": "connection_timeout"}]}, {"type": "重复阻塞检测逻辑", "severity": "HIGH", "pattern": "silent_duration", "count": 2, "locations": [{"file": "websocket/ws_client.py", "line": 680, "context": "self._log_warning(f\"检测到静默断开: {request['silent_duration']:.1f}秒\")", "match": "silent_duration"}, {"file": "websocket/enhanced_blocking_tracker.py", "line": 282, "context": "duration = details.get('silent_duration_seconds', 0)", "match": "silent_duration"}]}, {"type": "重复阻塞检测逻辑", "severity": "HIGH", "pattern": "silent_disconnect", "count": 2, "locations": [{"file": "websocket/ws_client.py", "line": 679, "context": "if request['type'] == 'silent_disconnect_check':", "match": "silent_disconnect"}, {"file": "websocket/ws_manager.py", "line": 11, "context": "log_websocket_silent_disconnect)", "match": "silent_disconnect"}]}, {"type": "日志详细程度不足", "severity": "MEDIUM", "description": "阻塞原因日志不够详细"}], "summary": {"critical": 0, "high": 7, "medium": 1, "total": 8}}