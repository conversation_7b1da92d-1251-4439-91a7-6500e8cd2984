#!/usr/bin/env python3
"""
🔥 基础核心测试：验证统一性修复的基础功能
确保修复点本身100%稳定
"""

import sys
import os
import unittest
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

class TestUnificationFixesBasic(unittest.TestCase):
    """基础核心测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = {
            'gate': {'t': 1735123456789, 'symbol': 'BTC_USDT'},
            'okx': {'ts': '1735123456789', 'symbol': 'BTC-USDT'},
            'bybit': {'ts': 1735123456789, 'symbol': 'BTCUSDT'}
        }
    
    def test_01_unified_timestamp_processor_import(self):
        """测试1：统一时间戳处理器导入"""
        try:
            from websocket.unified_timestamp_processor import (
                get_timestamp_processor,
                ensure_milliseconds_timestamp,
                get_synced_timestamp
            )
            self.assertTrue(True, "统一时间戳处理器导入成功")
        except ImportError as e:
            self.fail(f"统一时间戳处理器导入失败: {e}")
    
    def test_02_enhanced_blocking_tracker_import(self):
        """测试2：增强阻塞追踪器导入"""
        try:
            from websocket.enhanced_blocking_tracker import (
                get_blocking_tracker,
                log_websocket_data_received
            )
            self.assertTrue(True, "增强阻塞追踪器导入成功")
        except ImportError as e:
            self.fail(f"增强阻塞追踪器导入失败: {e}")
    
    def test_03_currency_adapter_import_with_fallback(self):
        """测试3：货币适配器导入及应急处理"""
        try:
            from exchanges.currency_adapter import get_exchange_symbol
            # 测试正常转换
            result = get_exchange_symbol("BTC-USDT", "gate", "spot")
            self.assertIsInstance(result, str, "货币适配器返回字符串")
        except ImportError:
            # 测试应急转换逻辑
            from websocket import _emergency_symbol_convert
            result = _emergency_symbol_convert("BTC-USDT", "gate", "spot")
            self.assertEqual(result, "BTC_USDT", "应急转换逻辑正确")
    
    def test_04_timestamp_format_normalization(self):
        """测试4：时间戳格式标准化"""
        from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp
        
        # 测试秒级时间戳
        sec_timestamp = 1735123456
        result = ensure_milliseconds_timestamp(sec_timestamp)
        self.assertEqual(result, 1735123456000, "秒级时间戳转换正确")
        
        # 测试毫秒级时间戳
        ms_timestamp = 1735123456789
        result = ensure_milliseconds_timestamp(ms_timestamp)
        self.assertEqual(result, 1735123456789, "毫秒级时间戳保持不变")
        
        # 测试纳秒级时间戳
        ns_timestamp = 1735123456789000000
        result = ensure_milliseconds_timestamp(ns_timestamp)
        self.assertEqual(result, 1735123456789, "纳秒级时间戳转换正确")
    
    def test_05_nested_field_extraction(self):
        """测试5：嵌套字段提取功能"""
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        processor = get_timestamp_processor("test")
        
        # 测试简单字段提取
        simple_data = {'ts': 1735123456789}
        result = processor._extract_nested_field(simple_data, 'ts')
        self.assertEqual(result, 1735123456789, "简单字段提取正确")
        
        # 测试嵌套字段提取
        nested_data = {'result': {'t': 1735123456789}}
        result = processor._extract_nested_field(nested_data, ('result', 't'))
        self.assertEqual(result, 1735123456789, "嵌套字段提取正确")
        
        # 测试不存在字段
        result = processor._extract_nested_field(simple_data, 'nonexistent')
        self.assertIsNone(result, "不存在字段返回None")
    
    def test_06_error_handling_consistency(self):
        """测试6：错误处理一致性"""
        # 测试三个WebSocket客户端的错误处理格式
        test_cases = [
            ("gate", "GATE"),
            ("bybit", "BYBIT"), 
            ("okx", "OKX")
        ]
        
        for exchange, expected_prefix in test_cases:
            # 检查错误格式模板
            error_format = f"❌ [{expected_prefix}-SPOT] WebSocket错误"
            self.assertIn("❌", error_format, f"{exchange}错误格式包含错误标识")
            self.assertIn(expected_prefix, error_format, f"{exchange}错误格式包含交易所标识")
    
    def test_07_threshold_consistency(self):
        """测试7：关键阈值一致性"""
        # 检查关键阈值是否统一为1000ms
        expected_threshold = 1000
        
        # 测试时间戳处理器阈值
        from websocket.unified_timestamp_processor import get_timestamp_processor
        processor = get_timestamp_processor("test")
        
        # 验证新鲜度检查阈值
        is_fresh, age = processor.validate_timestamp_freshness(
            int(time.time() * 1000) - 500,  # 500ms前的时间戳
            max_age_ms=expected_threshold
        )
        self.assertTrue(is_fresh, "500ms时间戳应该被认为是新鲜的")
        
        is_fresh, age = processor.validate_timestamp_freshness(
            int(time.time() * 1000) - 1500,  # 1500ms前的时间戳
            max_age_ms=expected_threshold
        )
        self.assertFalse(is_fresh, "1500ms时间戳应该被认为是过期的")
    
    def test_08_blocking_detection_unification(self):
        """测试8：阻塞检测统一性"""
        from websocket.enhanced_blocking_tracker import get_blocking_tracker
        
        tracker = get_blocking_tracker()
        initial_count = len(tracker.exchange_metrics)
        
        # 模拟数据接收
        tracker.update_exchange_metrics("test_exchange", "spot", "BTC-USDT", {"test": "data"})
        
        after_count = len(tracker.exchange_metrics)
        self.assertGreater(after_count, initial_count, "阻塞追踪器正确记录数据接收")
    
    def test_09_exchange_specific_removal(self):
        """测试9：交易所特定处理移除验证"""
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        # 测试三个交易所使用相同的处理逻辑
        exchanges = ["gate", "bybit", "okx"]
        results = []
        
        for exchange in exchanges:
            processor = get_timestamp_processor(exchange)
            # 使用相同的测试数据
            test_data = {'ts': 1735123456789, 't': 1735123456789}
            result = processor.get_synced_timestamp(test_data)
            results.append(result)
        
        # 验证所有交易所返回的时间戳格式一致（都是毫秒级整数）
        for result in results:
            self.assertIsInstance(result, int, "所有交易所返回整数时间戳")
            self.assertGreater(result, 1e12, "所有交易所返回毫秒级时间戳")
    
    def test_10_configuration_consistency(self):
        """测试10：配置一致性验证"""
        # 检查网络配置是否统一
        try:
            import sys
            sys.path.append('123')
            from config.network_config import NetworkConfig
            
            config = NetworkConfig()
            
            # 验证关键阈值统一为1000ms
            self.assertEqual(config.timestamp_tolerance, 1000, "时间戳容忍度统一为1000ms")
            self.assertEqual(config.sync_tolerance, 1000, "同步容忍度统一为1000ms")
            self.assertEqual(config.orderbook_timeout, 1000, "订单簿超时统一为1000ms")
            
        except ImportError:
            self.skipTest("网络配置模块不可用")

def run_basic_tests():
    """运行基础核心测试"""
    print("🧪 开始基础核心测试...")
    print("="*60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestUnificationFixesBasic)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 生成测试报告
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    success_rate = ((total_tests - failures - errors) / total_tests) * 100 if total_tests > 0 else 0
    
    print("\n" + "="*60)
    print("🧪 基础核心测试报告")
    print("="*60)
    print(f"📊 总测试数: {total_tests}")
    print(f"✅ 成功: {total_tests - failures - errors}")
    print(f"❌ 失败: {failures}")
    print(f"🚨 错误: {errors}")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if failures > 0:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if errors > 0:
        print(f"\n🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    if success_rate == 100:
        print("\n🎉 所有基础核心测试通过！修复点本身100%稳定！")
        return True
    else:
        print(f"\n⚠️ 基础测试通过率{success_rate:.1f}%，需要进一步修复")
        return False

if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
