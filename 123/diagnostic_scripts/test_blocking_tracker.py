#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试enhanced_blocking_tracker是否正常工作
验证阻塞检测和日志记录功能
"""

import time
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_blocking_tracker():
    """测试阻塞追踪器功能"""
    print("🧪 测试enhanced_blocking_tracker功能...")
    
    try:
        # 1. 导入阻塞追踪器
        from websocket.enhanced_blocking_tracker import (
            get_blocking_tracker, 
            log_websocket_data_received,
            log_websocket_connection_event
        )
        print("✅ 成功导入enhanced_blocking_tracker")
        
        # 2. 获取追踪器实例
        tracker = get_blocking_tracker()
        print(f"✅ 获取追踪器实例: {type(tracker).__name__}")
        
        # 3. 检查初始状态
        initial_metrics = len(tracker.exchange_metrics)
        print(f"📊 初始监控交易所数量: {initial_metrics}")
        
        # 4. 模拟正常数据接收
        print("\n📡 模拟正常数据接收...")
        exchanges = ["gate", "bybit", "okx"]
        for i, exchange in enumerate(exchanges):
            log_websocket_data_received(exchange, "spot", "BTC-USDT", {
                "timestamp": int(time.time() * 1000),
                "test_data": f"message_{i}"
            })
            print(f"   📨 {exchange}: 发送测试数据")
        
        # 5. 检查数据接收后的状态
        after_metrics = len(tracker.exchange_metrics)
        print(f"📊 数据接收后监控交易所数量: {after_metrics}")
        
        if after_metrics > initial_metrics:
            print("✅ 阻塞追踪器正确记录了数据接收")
        else:
            print("❌ 阻塞追踪器未记录数据接收")
        
        # 6. 检查各交易所状态
        print("\n📈 当前交易所状态:")
        current_time = time.time()
        for key, metrics in tracker.exchange_metrics.items():
            gap = current_time - metrics.last_data_time
            print(f"   {key}: 最后数据时间差 {gap:.1f}秒, 总消息数 {metrics.total_messages}")
        
        # 7. 模拟连接事件
        print("\n🔗 模拟连接事件...")
        log_websocket_connection_event("gate", "spot", "test_event", {
            "test": "connection_event"
        })
        print("✅ 连接事件已记录")
        
        # 8. 生成报告
        print("\n📋 生成阻塞报告...")
        tracker.generate_periodic_report(force=True)
        summary = tracker.get_blocking_summary()
        
        print("📊 阻塞摘要:")
        print(f"   总监控时间: {summary.get('total_monitoring_time_minutes', 0):.1f}分钟")
        print(f"   活跃交易所: {summary.get('active_exchanges', 0)}")
        print(f"   阻塞事件数: {summary.get('total_blocking_events', 0)}")
        print(f"   历史事件数: {len(tracker.blocking_history)}")
        
        # 9. 检查日志文件
        print("\n📁 检查日志文件...")
        log_dir = Path("logs")
        if log_dir.exists():
            log_files = list(log_dir.glob("websocket_blocking_*.log"))
            if log_files:
                latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
                print(f"✅ 找到阻塞日志文件: {latest_log}")
                
                # 读取最新日志内容
                try:
                    with open(latest_log, 'r', encoding='utf-8') as f:
                        content = f.read()
                    if content.strip():
                        lines = content.strip().split('\n')
                        print(f"📝 日志文件有 {len(lines)} 行内容")
                        print("📄 最新几行日志:")
                        for line in lines[-3:]:
                            print(f"   {line}")
                    else:
                        print("⚠️ 日志文件为空")
                except Exception as e:
                    print(f"❌ 读取日志文件失败: {e}")
            else:
                print("⚠️ 未找到阻塞日志文件")
        else:
            print("❌ logs目录不存在")
        
        # 10. 模拟阻塞检测
        print("\n⏳ 模拟阻塞检测...")
        print("   等待35秒以触发阻塞检测...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 等待超过阻塞阈值
        time.sleep(35)
        
        # 发送恢复数据
        log_websocket_data_received("gate", "spot", "BTC-USDT", {
            "timestamp": int(time.time() * 1000),
            "recovery": True
        })
        
        print("✅ 阻塞模拟完成")
        
        # 11. 检查是否检测到阻塞
        final_summary = tracker.get_blocking_summary()
        final_events = final_summary.get('total_blocking_events', 0)
        
        if final_events > 0:
            print(f"✅ 成功检测到 {final_events} 个阻塞事件")
        else:
            print("⚠️ 未检测到阻塞事件")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_blocking_tracker()
    if success:
        print("\n🎉 阻塞追踪器测试完成")
    else:
        print("\n💥 阻塞追踪器测试失败")
        sys.exit(1)
