#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳和时间基准潜在问题深度审计
根据修复提示词要求，检查潜在的时间戳或者时间基准潜在的问题
确保三交易所时间处理完全一致
"""

import time
import sys
import re
from pathlib import Path
from datetime import datetime
from collections import defaultdict

class TimestampBaselineAuditor:
    """时间戳和时间基准审计器"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.issues = []
        
        # 时间相关的关键模式
        self.timestamp_patterns = {
            'time_time': r'time\.time\(\)',
            'datetime_now': r'datetime\.now\(\)',
            'timestamp_ms': r'timestamp.*\*\s*1000|timestamp.*\/\s*1000',
            'time_offset': r'time_offset',
            'time_sync': r'time_sync|sync.*time',
            'server_time': r'server_time|serverTime',
            'local_time': r'local_time|localTime',
            'unix_timestamp': r'unix.*timestamp|timestamp.*unix',
            'milliseconds': r'milliseconds?|ms\b',
            'microseconds': r'microseconds?|us\b',
            'timezone': r'timezone|tz\b|utc|UTC',
            'time_diff': r'time_diff|time.*diff|diff.*time',
            'latency': r'latency|delay',
            'clock_skew': r'clock.*skew|skew.*clock'
        }
        
        # 关键文件
        self.key_files = [
            'websocket/unified_timestamp_processor.py',
            'websocket/gate_ws.py',
            'websocket/bybit_ws.py', 
            'websocket/okx_ws.py',
            'websocket/enhanced_blocking_tracker.py',
            'config/network_config.py',
            'exchanges/gate_exchange.py',
            'exchanges/bybit_exchange.py',
            'exchanges/okx_exchange.py'
        ]
    
    def run_audit(self):
        """运行完整的时间戳基准审计"""
        print("🕐 开始时间戳和时间基准潜在问题深度审计...")
        print("=" * 80)
        
        # 1. 检查时间获取方式的一致性
        self._audit_time_acquisition_consistency()
        
        # 2. 检查时间单位的一致性
        self._audit_time_unit_consistency()
        
        # 3. 检查时间同步机制
        self._audit_time_sync_mechanisms()
        
        # 4. 检查时间戳处理逻辑
        self._audit_timestamp_processing_logic()
        
        # 5. 检查时区处理
        self._audit_timezone_handling()
        
        # 6. 检查时间差计算
        self._audit_time_difference_calculations()
        
        # 7. 实时时间基准测试
        self._realtime_timestamp_baseline_test()
        
        # 8. 输出审计结果
        self._output_audit_results()
    
    def _audit_time_acquisition_consistency(self):
        """审计时间获取方式的一致性"""
        print("\n📊 1. 审计时间获取方式的一致性...")
        
        time_acquisition_methods = defaultdict(list)
        
        for file_path in self.key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查不同的时间获取方式
                methods = {
                    'time.time()': len(re.findall(r'time\.time\(\)', content)),
                    'datetime.now()': len(re.findall(r'datetime\.now\(\)', content)),
                    'datetime.utcnow()': len(re.findall(r'datetime\.utcnow\(\)', content)),
                    'int(time.time())': len(re.findall(r'int\(time\.time\(\)\)', content)),
                    'int(time.time() * 1000)': len(re.findall(r'int\(time\.time\(\)\s*\*\s*1000\)', content))
                }
                
                for method, count in methods.items():
                    if count > 0:
                        time_acquisition_methods[method].append({
                            'file': str(file_path),
                            'count': count
                        })
                        
            except Exception as e:
                print(f"⚠️ 无法读取文件 {file_path}: {e}")
                continue
        
        print("📈 时间获取方式统计:")
        for method, files in time_acquisition_methods.items():
            print(f"   {method}: {len(files)} 个文件使用")
            for file_info in files:
                print(f"     - {file_info['file']}: {file_info['count']} 次")
        
        # 检查是否有不一致的时间获取方式
        if len(time_acquisition_methods) > 2:  # 允许time.time()和毫秒版本共存
            self.issues.append({
                'type': '时间获取方式不一致',
                'severity': 'MEDIUM',
                'methods': dict(time_acquisition_methods)
            })
            print("⚠️ 发现多种时间获取方式，可能导致时间基准不一致")
        else:
            print("✅ 时间获取方式基本一致")
    
    def _audit_time_unit_consistency(self):
        """审计时间单位的一致性"""
        print("\n📊 2. 审计时间单位的一致性...")
        
        unit_patterns = {
            'seconds': r'seconds?|秒',
            'milliseconds': r'milliseconds?|毫秒|ms\b',
            'microseconds': r'microseconds?|微秒|us\b',
            'multiply_1000': r'\*\s*1000',
            'divide_1000': r'\/\s*1000'
        }
        
        unit_usage = defaultdict(list)
        
        for file_path in self.key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for unit, pattern in unit_patterns.items():
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        unit_usage[unit].append({
                            'file': str(file_path),
                            'count': len(matches)
                        })
                        
            except Exception as e:
                continue
        
        print("📈 时间单位使用统计:")
        for unit, files in unit_usage.items():
            print(f"   {unit}: {len(files)} 个文件使用")
        
        # 检查毫秒和秒的混用
        has_ms = 'milliseconds' in unit_usage or 'multiply_1000' in unit_usage
        has_seconds = 'seconds' in unit_usage
        
        if has_ms and has_seconds:
            print("⚠️ 发现毫秒和秒的混用，需要确保转换正确")
        else:
            print("✅ 时间单位使用一致")
    
    def _audit_time_sync_mechanisms(self):
        """审计时间同步机制"""
        print("\n📊 3. 审计时间同步机制...")
        
        # 检查unified_timestamp_processor
        processor_file = self.project_root / 'websocket/unified_timestamp_processor.py'
        if processor_file.exists():
            try:
                with open(processor_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键时间同步功能
                sync_features = {
                    'time_offset': 'time_offset' in content,
                    'server_time_sync': 'server.*time' in content.lower(),
                    'time_correction': 'time.*correct' in content.lower(),
                    'sync_status': 'time_synced' in content,
                    'sync_threshold': 'sync.*threshold' in content.lower()
                }
                
                print("📈 时间同步机制检查:")
                for feature, exists in sync_features.items():
                    status = "✅" if exists else "❌"
                    print(f"   {status} {feature}")
                
                if not all(sync_features.values()):
                    self.issues.append({
                        'type': '时间同步机制不完整',
                        'severity': 'HIGH',
                        'missing_features': [k for k, v in sync_features.items() if not v]
                    })
                    
            except Exception as e:
                print(f"❌ 无法检查时间同步机制: {e}")
        else:
            print("❌ unified_timestamp_processor.py 不存在")
    
    def _audit_timestamp_processing_logic(self):
        """审计时间戳处理逻辑"""
        print("\n📊 4. 审计时间戳处理逻辑...")
        
        # 检查三个交易所的时间戳处理
        ws_files = {
            'gate': 'websocket/gate_ws.py',
            'bybit': 'websocket/bybit_ws.py',
            'okx': 'websocket/okx_ws.py'
        }
        
        timestamp_logic = {}
        
        for exchange, file_path in ws_files.items():
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查时间戳处理模式
                logic = {
                    'has_timestamp_processor': 'timestamp_processor' in content,
                    'has_time_offset': 'time_offset' in content,
                    'has_server_time': 'server_time' in content.lower(),
                    'has_time_conversion': '*1000' in content or '/1000' in content,
                    'has_time_validation': 'timestamp.*valid' in content.lower()
                }
                
                timestamp_logic[exchange] = logic
                
            except Exception as e:
                continue
        
        print("📈 各交易所时间戳处理逻辑:")
        for exchange, logic in timestamp_logic.items():
            print(f"   {exchange.upper()}:")
            for feature, exists in logic.items():
                status = "✅" if exists else "❌"
                print(f"     {status} {feature}")
        
        # 检查一致性
        if len(set(str(logic) for logic in timestamp_logic.values())) > 1:
            self.issues.append({
                'type': '时间戳处理逻辑不一致',
                'severity': 'HIGH',
                'exchange_logic': timestamp_logic
            })
            print("⚠️ 发现交易所间时间戳处理逻辑不一致")
        else:
            print("✅ 交易所时间戳处理逻辑一致")
    
    def _audit_timezone_handling(self):
        """审计时区处理"""
        print("\n📊 5. 审计时区处理...")
        
        timezone_patterns = [
            r'timezone|tz\b',
            r'utc|UTC',
            r'gmt|GMT',
            r'local.*time',
            r'pytz',
            r'tzinfo'
        ]
        
        timezone_usage = []
        
        for file_path in self.key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in timezone_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        timezone_usage.append({
                            'file': str(file_path),
                            'pattern': pattern,
                            'matches': len(matches)
                        })
                        
            except Exception as e:
                continue
        
        if timezone_usage:
            print("📈 时区处理使用情况:")
            for usage in timezone_usage:
                print(f"   {usage['file']}: {usage['pattern']} ({usage['matches']} 次)")
            
            # 检查是否有明确的UTC处理
            has_utc = any('utc' in usage['pattern'].lower() for usage in timezone_usage)
            if not has_utc:
                self.issues.append({
                    'type': '缺少明确的UTC时区处理',
                    'severity': 'MEDIUM',
                    'usage': timezone_usage
                })
                print("⚠️ 未发现明确的UTC时区处理")
        else:
            print("⚠️ 未发现明确的时区处理，可能存在时区混乱风险")
    
    def _audit_time_difference_calculations(self):
        """审计时间差计算"""
        print("\n📊 6. 审计时间差计算...")
        
        time_diff_patterns = [
            r'time.*-.*time',
            r'timestamp.*-.*timestamp',
            r'current_time.*-.*last_time',
            r'now.*-.*start',
            r'end.*-.*start',
            r'gap\s*=',
            r'diff\s*=',
            r'elapsed\s*='
        ]
        
        time_diff_usage = []
        
        for file_path in self.key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    for pattern in time_diff_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            time_diff_usage.append({
                                'file': str(file_path),
                                'line': i,
                                'content': line.strip(),
                                'pattern': pattern
                            })
                            
            except Exception as e:
                continue
        
        if time_diff_usage:
            print(f"📈 发现 {len(time_diff_usage)} 处时间差计算:")
            for usage in time_diff_usage[:10]:  # 只显示前10个
                print(f"   {usage['file']}:{usage['line']} - {usage['content'][:60]}...")
            
            if len(time_diff_usage) > 10:
                print(f"   ... 还有 {len(time_diff_usage) - 10} 处")
        else:
            print("⚠️ 未发现明显的时间差计算")
    
    def _realtime_timestamp_baseline_test(self):
        """实时时间基准测试"""
        print("\n📊 7. 实时时间基准测试...")
        
        try:
            # 测试不同时间获取方式的一致性
            test_start = time.time()
            
            timestamps = {
                'time.time()': time.time(),
                'time.time() * 1000': int(time.time() * 1000),
                'datetime.now().timestamp()': datetime.now().timestamp()
            }
            
            test_end = time.time()
            test_duration = (test_end - test_start) * 1000  # 毫秒
            
            print(f"📈 时间基准测试结果 (测试耗时: {test_duration:.2f}ms):")
            base_time = timestamps['time.time()']
            
            for method, timestamp in timestamps.items():
                if 'time.time()' == method:
                    print(f"   {method}: {timestamp:.6f} (基准)")
                elif '* 1000' in method:
                    converted = timestamp / 1000
                    diff = abs(converted - base_time) * 1000
                    print(f"   {method}: {timestamp} (差异: {diff:.2f}ms)")
                else:
                    diff = abs(timestamp - base_time) * 1000
                    print(f"   {method}: {timestamp:.6f} (差异: {diff:.2f}ms)")
            
            # 检查时间戳处理器
            try:
                sys.path.insert(0, str(self.project_root))
                from websocket.unified_timestamp_processor import get_timestamp_processor
                
                for exchange in ['gate', 'bybit', 'okx']:
                    processor = get_timestamp_processor(exchange)
                    if hasattr(processor, 'time_offset'):
                        print(f"   {exchange} 时间偏移: {processor.time_offset}ms")
                    if hasattr(processor, 'time_synced'):
                        print(f"   {exchange} 同步状态: {processor.time_synced}")
                        
            except Exception as e:
                print(f"⚠️ 无法测试时间戳处理器: {e}")
                
        except Exception as e:
            print(f"❌ 实时时间基准测试失败: {e}")
    
    def _output_audit_results(self):
        """输出审计结果"""
        print("\n" + "=" * 80)
        print("📋 时间戳和时间基准审计结果")
        print("=" * 80)
        
        if not self.issues:
            print("🎉 恭喜！未发现时间戳和时间基准相关问题")
            print("✅ 时间处理机制符合三交易所一致性要求")
            return
        
        print(f"🚨 发现 {len(self.issues)} 个潜在问题:")
        
        for i, issue in enumerate(self.issues, 1):
            print(f"\n{i}. [{issue['severity']}] {issue['type']}")
            if 'methods' in issue:
                print("   涉及方法:")
                for method, files in issue['methods'].items():
                    print(f"     - {method}: {len(files)} 个文件")
            elif 'missing_features' in issue:
                print(f"   缺失功能: {', '.join(issue['missing_features'])}")
            elif 'exchange_logic' in issue:
                print("   交易所差异:")
                for exchange, logic in issue['exchange_logic'].items():
                    print(f"     - {exchange}: {logic}")
        
        print("\n🔧 修复建议:")
        print("1. 统一所有时间获取方式，建议使用time.time()")
        print("2. 明确时间单位，统一使用秒或毫秒")
        print("3. 确保所有交易所使用相同的时间同步机制")
        print("4. 添加明确的UTC时区处理")
        print("5. 验证时间差计算的正确性")

if __name__ == "__main__":
    auditor = TimestampBaselineAuditor()
    auditor.run_audit()
