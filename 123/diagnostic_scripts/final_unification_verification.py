#!/usr/bin/env python3
"""
🔥 最终统一性验证脚本
验证三交易所运行逻辑和错误处理逻辑100%统一
"""

import os
import sys
import re
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

class FinalUnificationVerifier:
    """最终统一性验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.results = {
            'blocking_detection_unified': False,
            'error_handling_unified': False,
            'exchange_specific_removed': False,
            'threshold_unified': False,
            'overall_score': 0.0,
            'issues': []
        }
    
    def run_verification(self) -> Dict[str, Any]:
        """执行完整的统一性验证"""
        print("🔥 开始最终统一性验证...")
        
        # 1. 验证阻塞检测统一性
        self._verify_blocking_detection_unification()
        
        # 2. 验证错误处理统一性
        self._verify_error_handling_unification()
        
        # 3. 验证交易所特定处理移除
        self._verify_exchange_specific_removal()
        
        # 4. 验证关键阈值统一性
        self._verify_threshold_unification()
        
        # 5. 计算总体评分
        self._calculate_overall_score()
        
        # 6. 生成报告
        self._generate_report()
        
        return self.results
    
    def _verify_blocking_detection_unification(self):
        """验证阻塞检测统一性"""
        print("\n📊 验证阻塞检测统一性...")
        
        issues = []
        
        # 检查是否还有重复的阻塞检测逻辑
        blocking_patterns = [
            'data_flow_timeout', 'last_data_time', 'silent_duration',
            'max_silent_duration', '数据流阻塞', 'blocking'
        ]
        
        files_to_check = [
            'websocket/unified_timestamp_processor.py',
            'websocket/gate_ws.py',
            'websocket/bybit_ws.py', 
            'websocket/okx_ws.py',
            'websocket/ws_client.py'
        ]
        
        duplicate_detections = 0
        
        for file_path in files_to_check:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有重复的阻塞检测逻辑
            for pattern in blocking_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if len(matches) > 1:  # 允许一次提及，但不允许重复实现
                    duplicate_detections += len(matches) - 1
                    issues.append(f"{file_path}: 发现{len(matches)}次'{pattern}'重复")
        
        # 检查enhanced_blocking_tracker是否是唯一的阻塞检测器
        tracker_file = self.project_root / 'websocket/enhanced_blocking_tracker.py'
        if tracker_file.exists():
            print("✅ enhanced_blocking_tracker.py 存在")
        else:
            issues.append("❌ enhanced_blocking_tracker.py 不存在")
            duplicate_detections += 10
        
        self.results['blocking_detection_unified'] = duplicate_detections == 0
        if issues:
            self.results['issues'].extend(issues)
        
        print(f"📊 阻塞检测统一性: {'✅ 通过' if duplicate_detections == 0 else f'❌ 发现{duplicate_detections}个重复'}")
    
    def _verify_error_handling_unification(self):
        """验证错误处理统一性"""
        print("\n📊 验证错误处理统一性...")
        
        issues = []
        
        # 检查三个WebSocket客户端的错误处理格式
        ws_files = [
            ('websocket/gate_ws.py', 'GATE'),
            ('websocket/bybit_ws.py', 'BYBIT'),
            ('websocket/okx_ws.py', 'OKX')
        ]
        
        error_formats = {}
        
        for file_path, exchange in ws_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
            
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查错误日志格式
            error_patterns = [
                rf'❌ \[{exchange}-.*?\]',  # 统一的错误格式
                rf'🔧 \[{exchange}-.*?\]',  # 统一的智能过滤格式
                rf'⚠️ \[{exchange}-.*?\]'   # 统一的警告格式
            ]
            
            format_count = 0
            for pattern in error_patterns:
                matches = re.findall(pattern, content)
                format_count += len(matches)
            
            error_formats[exchange] = format_count
        
        # 检查格式一致性
        if len(set(error_formats.values())) > 1:
            issues.append(f"错误处理格式不一致: {error_formats}")
        
        self.results['error_handling_unified'] = len(issues) == 0
        if issues:
            self.results['issues'].extend(issues)
        
        print(f"📊 错误处理统一性: {'✅ 通过' if len(issues) == 0 else '❌ 不一致'}")
    
    def _verify_exchange_specific_removal(self):
        """验证交易所特定处理移除"""
        print("\n📊 验证交易所特定处理移除...")
        
        issues = []
        
        # 检查是否还有交易所特定处理
        exchange_specific_patterns = [
            r'if.*exchange.*==.*["\']gate["\']',
            r'if.*exchange.*==.*["\']bybit["\']',
            r'if.*exchange.*==.*["\']okx["\']',
            r'elif.*exchange.*==.*["\']gate["\']',
            r'elif.*exchange.*==.*["\']bybit["\']',
            r'elif.*exchange.*==.*["\']okx["\']'
        ]
        
        files_to_check = [
            'websocket/unified_timestamp_processor.py',
            'websocket/enhanced_blocking_tracker.py',
            'core/universal_token_system.py',
            'core/unified_symbol_validator.py'
        ]
        
        specific_logic_count = 0
        
        for file_path in files_to_check:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
            
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for pattern in exchange_specific_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    specific_logic_count += len(matches)
                    issues.append(f"{file_path}: 发现交易所特定处理 - {pattern}")
        
        self.results['exchange_specific_removed'] = specific_logic_count == 0
        if issues:
            self.results['issues'].extend(issues)
        
        print(f"📊 交易所特定处理移除: {'✅ 通过' if specific_logic_count == 0 else f'❌ 发现{specific_logic_count}个特定处理'}")
    
    def _verify_threshold_unification(self):
        """验证关键阈值统一性"""
        print("\n📊 验证关键阈值统一性...")
        
        issues = []
        
        # 检查关键阈值配置
        threshold_configs = {
            'network_config.py': {
                'timestamp_tolerance': 1000,
                'sync_tolerance': 1000,
                'orderbook_timeout': 1000,
                'ws_heartbeat_interval': 20,
                'ws_connect_timeout': 10
            },
            'data_snapshot_validator.py': {
                'max_snapshot_age_ms': 1000,
                'max_timestamp_diff_ms': 1000
            },
            'orderbook_validator.py': {
                'max_time_diff_ms': 1000
            }
        }
        
        for file_name, expected_values in threshold_configs.items():
            file_path = self.project_root / 'config' / file_name if 'config' in file_name else self.project_root / 'core' / file_name if 'core' in file_name else self.project_root / 'websocket' / file_name
            
            if not file_path.exists():
                continue
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for param, expected_value in expected_values.items():
                # 查找参数定义
                pattern = rf'{param}.*?=.*?(\d+)'
                matches = re.findall(pattern, content)
                
                if matches:
                    actual_value = int(matches[0])
                    if actual_value != expected_value:
                        issues.append(f"{file_name}: {param}={actual_value}, 期望={expected_value}")
                else:
                    issues.append(f"{file_name}: 未找到{param}参数")
        
        self.results['threshold_unified'] = len(issues) == 0
        if issues:
            self.results['issues'].extend(issues)
        
        print(f"📊 关键阈值统一性: {'✅ 通过' if len(issues) == 0 else '❌ 不一致'}")
    
    def _calculate_overall_score(self):
        """计算总体评分"""
        checks = [
            self.results['blocking_detection_unified'],
            self.results['error_handling_unified'], 
            self.results['exchange_specific_removed'],
            self.results['threshold_unified']
        ]
        
        passed_checks = sum(checks)
        total_checks = len(checks)
        
        self.results['overall_score'] = (passed_checks / total_checks) * 100
    
    def _generate_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("🔥 最终统一性验证报告")
        print("="*60)
        
        print(f"📊 总体评分: {self.results['overall_score']:.1f}%")
        print(f"✅ 阻塞检测统一: {'通过' if self.results['blocking_detection_unified'] else '失败'}")
        print(f"✅ 错误处理统一: {'通过' if self.results['error_handling_unified'] else '失败'}")
        print(f"✅ 交易所特定处理移除: {'通过' if self.results['exchange_specific_removed'] else '失败'}")
        print(f"✅ 关键阈值统一: {'通过' if self.results['threshold_unified'] else '失败'}")
        
        if self.results['issues']:
            print(f"\n❌ 发现 {len(self.results['issues'])} 个问题:")
            for i, issue in enumerate(self.results['issues'], 1):
                print(f"  {i}. {issue}")
        else:
            print("\n🎉 所有检查通过！三交易所运行逻辑和错误处理逻辑100%统一！")
        
        # 保存详细报告
        report_file = self.project_root / 'diagnostic_scripts' / 'final_unification_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    verifier = FinalUnificationVerifier()
    results = verifier.run_verification()
    
    # 返回退出码
    exit_code = 0 if results['overall_score'] == 100.0 else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
