#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复时间戳同步问题
手动触发三交易所的时间同步，确保时间基准一致
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

async def fix_timestamp_sync():
    """修复时间戳同步问题"""
    print("🕐 开始修复时间戳同步问题...")
    print("=" * 80)
    
    try:
        # 1. 导入时间戳处理器
        from websocket.unified_timestamp_processor import (
            initialize_all_timestamp_processors,
            check_all_timestamp_sync_health,
            get_timestamp_processor
        )
        
        print("✅ 成功导入时间戳处理器模块")
        
        # 2. 检查当前同步状态
        print("\n📊 检查当前时间同步状态...")
        health_status = await check_all_timestamp_sync_health()
        
        for exchange, status in health_status.items():
            if 'error' in status:
                print(f"❌ {exchange.upper()}: 错误 - {status['error']}")
            else:
                sync_status = "✅ 已同步" if status['time_synced'] else "❌ 未同步"
                health_level = status['health_level']
                offset = status.get('time_offset_ms', 0)
                print(f"   {exchange.upper()}: {sync_status}, 偏移: {offset}ms, 健康: {health_level}")
        
        # 3. 强制执行时间同步
        print("\n🔄 强制执行时间同步...")
        sync_results = await initialize_all_timestamp_processors(force_sync=True)
        
        success_count = sum(1 for success in sync_results.values() if success)
        total_count = len(sync_results)
        
        print(f"📈 同步结果: {success_count}/{total_count} 成功")
        for exchange, success in sync_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"   {exchange.upper()}: {status}")
        
        # 4. 再次检查同步状态
        print("\n📊 检查修复后的时间同步状态...")
        health_status_after = await check_all_timestamp_sync_health()
        
        all_healthy = True
        for exchange, status in health_status_after.items():
            if 'error' in status:
                print(f"❌ {exchange.upper()}: 错误 - {status['error']}")
                all_healthy = False
            else:
                sync_status = "✅ 已同步" if status['time_synced'] else "❌ 未同步"
                health_level = status['health_level']
                offset = status.get('time_offset_ms', 0)
                is_healthy = status.get('is_healthy', False)
                
                if not is_healthy:
                    all_healthy = False
                
                print(f"   {exchange.upper()}: {sync_status}, 偏移: {offset}ms, 健康: {health_level}")
        
        # 5. 测试时间戳获取一致性
        print("\n🧪 测试时间戳获取一致性...")
        
        test_data = {"timestamp": int(time.time() * 1000)}
        timestamps = {}
        
        for exchange in ['gate', 'bybit', 'okx']:
            try:
                processor = get_timestamp_processor(exchange)
                timestamp = processor.get_synced_timestamp(test_data)
                timestamps[exchange] = timestamp
                print(f"   {exchange.upper()}: {timestamp}")
            except Exception as e:
                print(f"   {exchange.upper()}: 错误 - {e}")
                timestamps[exchange] = None
        
        # 检查时间戳差异
        valid_timestamps = [ts for ts in timestamps.values() if ts is not None]
        if len(valid_timestamps) > 1:
            max_diff = max(valid_timestamps) - min(valid_timestamps)
            print(f"📊 最大时间戳差异: {max_diff}ms")
            
            if max_diff < 1000:  # 小于1秒
                print("✅ 时间戳差异在可接受范围内")
            else:
                print("⚠️ 时间戳差异过大，可能存在同步问题")
                all_healthy = False
        
        # 6. 输出修复结果
        print("\n" + "=" * 80)
        if all_healthy and success_count == total_count:
            print("🎉 时间戳同步修复成功！")
            print("✅ 所有交易所时间同步正常")
            print("✅ 时间基准一致性已确保")
        elif success_count > 0:
            print("⚠️ 时间戳同步部分修复")
            print(f"   成功: {success_count}/{total_count} 个交易所")
            failed_exchanges = [ex for ex, success in sync_results.items() if not success]
            if failed_exchanges:
                print(f"   失败: {', '.join(failed_exchanges)}")
        else:
            print("❌ 时间戳同步修复失败")
            print("   建议检查网络连接和API访问权限")
        
        return all_healthy and success_count == total_count
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_individual_sync():
    """测试单个交易所的时间同步"""
    print("\n🔧 测试单个交易所时间同步...")
    
    try:
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        for exchange in ['gate', 'bybit', 'okx']:
            print(f"\n📡 测试 {exchange.upper()} 时间同步...")
            
            processor = get_timestamp_processor(exchange)
            
            # 检查初始状态
            initial_status = processor.get_sync_status()
            print(f"   初始状态: 同步={initial_status['time_synced']}, 偏移={initial_status['time_offset_ms']}ms")
            
            # 尝试同步
            sync_success = await processor.sync_time(force=True)
            
            if sync_success:
                # 检查同步后状态
                after_status = processor.get_sync_status()
                print(f"   ✅ 同步成功: 偏移={after_status['time_offset_ms']}ms")
            else:
                print(f"   ❌ 同步失败")
                
    except Exception as e:
        print(f"❌ 单个同步测试失败: {e}")

if __name__ == "__main__":
    async def main():
        success = await fix_timestamp_sync()
        
        if not success:
            print("\n🔧 尝试单个交易所同步测试...")
            await test_individual_sync()
        
        print("\n📋 修复完成")
        return success
    
    result = asyncio.run(main())
    if not result:
        sys.exit(1)
