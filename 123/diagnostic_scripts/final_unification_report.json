{"blocking_detection_unified": false, "error_handling_unified": false, "exchange_specific_removed": true, "threshold_unified": true, "overall_score": 50.0, "issues": ["websocket/unified_timestamp_processor.py: 发现4次'数据流阻塞'重复", "websocket/unified_timestamp_processor.py: 发现3次'blocking'重复", "websocket/gate_ws.py: 发现3次'blocking'重复", "websocket/bybit_ws.py: 发现3次'blocking'重复", "websocket/okx_ws.py: 发现2次'last_data_time'重复", "websocket/okx_ws.py: 发现5次'blocking'重复", "websocket/ws_client.py: 发现4次'silent_duration'重复", "websocket/ws_client.py: 发现3次'max_silent_duration'重复", "websocket/ws_client.py: 发现5次'blocking'重复", "错误处理格式不一致: {'GATE': 8, 'BYBIT': 5, 'OKX': 6}"]}