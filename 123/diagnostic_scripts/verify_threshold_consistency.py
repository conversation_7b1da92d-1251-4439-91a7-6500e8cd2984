#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证三交易所关键阈值完全一致性
根据修复提示词要求，确保三交易所运行逻辑和错误处理逻辑一致和关键阈值一致
"""

import re
import sys
from pathlib import Path
from collections import defaultdict

class ThresholdConsistencyVerifier:
    """阈值一致性验证器"""
    
    def __init__(self):
        self.project_root = Path(".")
        self.issues = []
        
        # 关键阈值配置项
        self.key_thresholds = {
            'connection_timeout': r'connection_timeout\s*=\s*(\d+)',
            'heartbeat_interval': r'heartbeat_interval\s*=\s*(\d+)',
            'reconnect_delay': r'reconnect_delay\s*=\s*([\d.]+)',
            'max_reconnect_attempts': r'max_reconnect_attempts\s*=\s*(\d+)',
            'ws_connect_timeout': r'ws_connect_timeout\s*=\s*(\d+)',
            'ping_interval': r'ping_interval\s*=\s*(\d+)',
            'pong_timeout': r'pong_timeout\s*=\s*(\d+)',
            'subscribe_timeout': r'subscribe_timeout\s*=\s*(\d+)',
            'data_timeout': r'data_timeout\s*=\s*(\d+)',
            'blocking_threshold': r'blocking_threshold\s*=\s*(\d+)',
            'critical_threshold': r'critical_threshold\s*=\s*(\d+)'
        }
        
        # WebSocket客户端文件
        self.ws_files = {
            'gate': 'websocket/gate_ws.py',
            'bybit': 'websocket/bybit_ws.py',
            'okx': 'websocket/okx_ws.py'
        }
    
    def run_verification(self):
        """运行完整的阈值一致性验证"""
        print("🔍 开始三交易所关键阈值一致性验证...")
        print("=" * 80)
        
        # 1. 提取各交易所的阈值配置
        exchange_configs = self._extract_exchange_configs()
        
        # 2. 比较阈值一致性
        self._compare_threshold_consistency(exchange_configs)
        
        # 3. 检查网络配置统一性
        self._verify_network_config_consistency()
        
        # 4. 检查enhanced_blocking_tracker阈值
        self._verify_blocking_tracker_thresholds()
        
        # 5. 输出验证结果
        self._output_verification_results()
    
    def _extract_exchange_configs(self):
        """提取各交易所的阈值配置"""
        print("\n📊 1. 提取各交易所阈值配置...")
        
        exchange_configs = {}
        
        for exchange, file_path in self.ws_files.items():
            full_path = self.project_root / file_path
            if not full_path.exists():
                print(f"❌ {file_path} 不存在")
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                config = {}
                for threshold_name, pattern in self.key_thresholds.items():
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        # 取最后一个匹配值（可能有多个定义）
                        config[threshold_name] = matches[-1]
                
                exchange_configs[exchange] = config
                print(f"✅ {exchange}: 提取到 {len(config)} 个阈值配置")
                
            except Exception as e:
                print(f"❌ 读取 {file_path} 失败: {e}")
                continue
        
        return exchange_configs
    
    def _compare_threshold_consistency(self, exchange_configs):
        """比较阈值一致性"""
        print("\n📊 2. 比较阈值一致性...")
        
        # 收集所有阈值项
        all_thresholds = set()
        for config in exchange_configs.values():
            all_thresholds.update(config.keys())
        
        inconsistencies = []
        
        for threshold in all_thresholds:
            values = {}
            for exchange, config in exchange_configs.items():
                if threshold in config:
                    values[exchange] = config[threshold]
            
            if len(set(values.values())) > 1:
                inconsistencies.append({
                    'threshold': threshold,
                    'values': values
                })
                print(f"❌ {threshold} 不一致:")
                for exchange, value in values.items():
                    print(f"   {exchange}: {value}")
            else:
                if values:  # 如果有值且一致
                    print(f"✅ {threshold}: {list(values.values())[0]} (一致)")
        
        if inconsistencies:
            self.issues.extend(inconsistencies)
            print(f"\n🚨 发现 {len(inconsistencies)} 个阈值不一致问题")
        else:
            print("\n✅ 所有阈值配置一致")
    
    def _verify_network_config_consistency(self):
        """验证网络配置统一性"""
        print("\n📊 3. 验证网络配置统一性...")
        
        network_config_file = self.project_root / 'config/network_config.py'
        if not network_config_file.exists():
            print("❌ config/network_config.py 不存在")
            return
        
        try:
            with open(network_config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查统一配置项
            unified_configs = {
                'ws_connect_timeout': r"ws_connect_timeout=int\(os\.getenv\('WS_CONNECT_TIMEOUT',\s*'(\d+)'\)\)",
                'ws_heartbeat_interval': r"ws_heartbeat_interval=int\(os\.getenv\('WS_HEARTBEAT_INTERVAL',\s*'(\d+)'\)\)",
                'ws_reconnect_delay': r"ws_reconnect_delay=float\(os\.getenv\('WS_RECONNECT_DELAY',\s*'([\d.]+)'\)\)",
                'timestamp_tolerance': r"timestamp_tolerance=int\(os\.getenv\('TIMESTAMP_TOLERANCE',\s*'(\d+)'\)\)",
                'sync_tolerance': r"sync_tolerance=int\(os\.getenv\('SYNC_TOLERANCE',\s*'(\d+)'\)\)",
                'orderbook_timeout': r"orderbook_timeout=int\(os\.getenv\('ORDERBOOK_TIMEOUT',\s*'(\d+)'\)\)"
            }
            
            network_values = {}
            for config_name, pattern in unified_configs.items():
                matches = re.findall(pattern, content)
                if matches:
                    network_values[config_name] = matches[0]
                    print(f"✅ {config_name}: {matches[0]}")
                else:
                    print(f"⚠️ {config_name}: 未找到配置")
            
            # 检查是否有注释说明统一性
            if '统一1000ms' in content:
                print("✅ 发现统一阈值修复注释")
            if '统一为20秒' in content:
                print("✅ 发现统一心跳间隔注释")
                
        except Exception as e:
            print(f"❌ 读取网络配置失败: {e}")
    
    def _verify_blocking_tracker_thresholds(self):
        """验证阻塞追踪器阈值"""
        print("\n📊 4. 验证阻塞追踪器阈值...")
        
        tracker_file = self.project_root / 'websocket/enhanced_blocking_tracker.py'
        if not tracker_file.exists():
            print("❌ enhanced_blocking_tracker.py 不存在")
            return
        
        try:
            with open(tracker_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查阻塞检测阈值
            blocking_patterns = {
                'blocking_threshold': r'self\.blocking_threshold\s*=\s*(\d+)',
                'critical_threshold': r'self\.critical_threshold\s*=\s*(\d+)'
            }
            
            for threshold_name, pattern in blocking_patterns.items():
                matches = re.findall(pattern, content)
                if matches:
                    print(f"✅ {threshold_name}: {matches[0]}秒")
                else:
                    print(f"⚠️ {threshold_name}: 未找到配置")
                    
        except Exception as e:
            print(f"❌ 读取阻塞追踪器配置失败: {e}")
    
    def _output_verification_results(self):
        """输出验证结果"""
        print("\n" + "=" * 80)
        print("📋 阈值一致性验证结果")
        print("=" * 80)
        
        if not self.issues:
            print("🎉 恭喜！所有关键阈值配置完全一致")
            print("✅ 符合'确保三交易所运行逻辑和错误处理逻辑一致和关键阈值一致'的要求")
            return
        
        print(f"🚨 发现 {len(self.issues)} 个阈值不一致问题:")
        
        for i, issue in enumerate(self.issues, 1):
            print(f"\n{i}. {issue['threshold']} 不一致:")
            for exchange, value in issue['values'].items():
                print(f"   {exchange}: {value}")
        
        print("\n🔧 修复建议:")
        print("1. 统一所有交易所的关键阈值配置")
        print("2. 使用config/network_config.py中的统一配置")
        print("3. 确保所有WebSocket客户端从统一配置读取参数")
        print("4. 移除硬编码的阈值，使用环境变量或配置文件")

if __name__ == "__main__":
    verifier = ThresholdConsistencyVerifier()
    verifier.run_verification()
