#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阻塞检测重复逻辑精确诊断脚本
根据修复提示词要求，彻底清理重复的阻塞检测函数，确保只有enhanced_blocking_tracker处理
"""

import os
import re
import json
from pathlib import Path
from collections import defaultdict
from datetime import datetime

class BlockingDetectionAuditor:
    """阻塞检测重复逻辑审计器"""
    
    def __init__(self):
        self.project_root = Path(".")  # 当前目录就是123
        self.issues = []
        self.duplicate_count = 0
        
        # 阻塞检测相关的关键词模式
        self.blocking_patterns = {
            'data_flow_timeout': r'data_flow_timeout',
            'last_data_time': r'last_data_time',
            'silent_duration': r'silent_duration',
            'max_silent_duration': r'max_silent_duration',
            'blocking_detection': r'blocking.*detection|detection.*blocking',
            'data_staleness': r'data_staleness|staleness.*data',
            'silent_disconnect': r'silent.*disconnect|disconnect.*silent',
            'websocket_blocking': r'websocket.*blocking|blocking.*websocket',
            'data_flow_blocking': r'数据流阻塞|data.*flow.*blocking',
            'connection_timeout': r'connection.*timeout|timeout.*connection'
        }
        
        # 需要检查的关键文件
        self.key_files = [
            'websocket/unified_timestamp_processor.py',
            'websocket/gate_ws.py',
            'websocket/bybit_ws.py',
            'websocket/okx_ws.py',
            'websocket/ws_client.py',
            'websocket/ws_manager.py',
            'websocket/enhanced_blocking_tracker.py'
        ]
    
    def run_audit(self):
        """运行完整的阻塞检测审计"""
        print("🔍 开始阻塞检测重复逻辑精确诊断...")
        print(f"📅 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 1. 检查重复的阻塞检测逻辑
        self._audit_duplicate_blocking_logic()
        
        # 2. 检查enhanced_blocking_tracker的唯一性
        self._audit_blocking_tracker_uniqueness()
        
        # 3. 检查日志详细程度
        self._audit_logging_detail()
        
        # 4. 检查时间戳处理器中的交易所特定处理
        self._audit_timestamp_processor_specificity()
        
        # 5. 生成修复建议
        self._generate_fix_recommendations()
        
        # 6. 输出诊断结果
        self._output_results()
    
    def _audit_duplicate_blocking_logic(self):
        """审计重复的阻塞检测逻辑"""
        print("\n📊 1. 审计重复的阻塞检测逻辑...")
        
        pattern_occurrences = defaultdict(list)
        
        for file_path in self.key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')

                # 检查每个阻塞检测模式
                for pattern_name, pattern_regex in self.blocking_patterns.items():
                    matches = list(re.finditer(pattern_regex, content, re.IGNORECASE))

                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        context = lines[line_num - 1].strip() if line_num <= len(lines) else ""

                        pattern_occurrences[pattern_name].append({
                            'file': str(file_path),
                            'line': line_num,
                            'context': context,
                            'match': match.group()
                        })
            except Exception as e:
                print(f"⚠️ 无法读取文件 {file_path}: {e}")
                continue

        # 分析重复情况
        for pattern_name, occurrences in pattern_occurrences.items():
            if len(occurrences) > 1:
                # 检查是否是真正的重复实现（排除注释和删除标记）
                real_implementations = []
                for occ in occurrences:
                    context = occ['context']
                    # 排除注释、删除标记、导入语句
                    if not (context.strip().startswith('#') or 
                           '删除' in context or 
                           'import' in context or
                           'from' in context):
                        real_implementations.append(occ)
                
                if len(real_implementations) > 1:
                    self.duplicate_count += len(real_implementations) - 1
                    self.issues.append({
                        'type': '重复阻塞检测逻辑',
                        'severity': 'HIGH',
                        'pattern': pattern_name,
                        'count': len(real_implementations),
                        'locations': real_implementations
                    })
                    
                    print(f"❌ 发现重复: {pattern_name} - {len(real_implementations)}处实现")
                    for impl in real_implementations:
                        print(f"   📍 {impl['file']}:{impl['line']} - {impl['context'][:60]}...")
        
        if self.duplicate_count == 0:
            print("✅ 未发现重复的阻塞检测逻辑")
        else:
            print(f"🚨 总计发现 {self.duplicate_count} 处重复的阻塞检测逻辑")
    
    def _audit_blocking_tracker_uniqueness(self):
        """审计enhanced_blocking_tracker的唯一性"""
        print("\n📊 2. 审计enhanced_blocking_tracker的唯一性...")
        
        tracker_file = self.project_root / 'websocket/enhanced_blocking_tracker.py'
        if not tracker_file.exists():
            self.issues.append({
                'type': '缺失核心组件',
                'severity': 'CRITICAL',
                'description': 'enhanced_blocking_tracker.py 不存在'
            })
            print("❌ enhanced_blocking_tracker.py 不存在")
            return
        
        print("✅ enhanced_blocking_tracker.py 存在")
        
        # 检查其他文件是否还在做阻塞检测
        blocking_functions = [
            'update_symbol_data_flow',
            'log_websocket_data_received',
            '_handle_data_flow_blocking',
            '_monitor_data_flow'
        ]
        
        other_blocking_implementations = []
        
        for file_path in self.key_files:
            if 'enhanced_blocking_tracker.py' in str(file_path):
                continue  # 跳过tracker本身
                
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for func_name in blocking_functions:
                    if f"def {func_name}" in content:
                        other_blocking_implementations.append({
                            'file': str(file_path),
                            'function': func_name
                        })
            except:
                continue
        
        if other_blocking_implementations:
            self.issues.append({
                'type': '阻塞检测职责分散',
                'severity': 'HIGH',
                'implementations': other_blocking_implementations
            })
            print("❌ 发现其他文件中的阻塞检测实现:")
            for impl in other_blocking_implementations:
                print(f"   📍 {impl['file']} - {impl['function']}")
        else:
            print("✅ 阻塞检测职责集中在enhanced_blocking_tracker中")
    
    def _audit_logging_detail(self):
        """审计日志详细程度"""
        print("\n📊 3. 审计日志详细程度...")
        
        # 检查是否有详细的阻塞原因日志
        log_patterns = [
            r'silent_duration_seconds',
            r'阻塞原因',
            r'blocking.*cause',
            r'数据流.*阻塞.*原因'
        ]
        
        detailed_logging_files = []
        
        for file_path in self.key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                has_detailed_logging = any(
                    re.search(pattern, content, re.IGNORECASE) 
                    for pattern in log_patterns
                )
                
                if has_detailed_logging:
                    detailed_logging_files.append(str(file_path))
            except:
                continue
        
        if len(detailed_logging_files) < 2:  # 至少应该有tracker和一个使用者
            self.issues.append({
                'type': '日志详细程度不足',
                'severity': 'MEDIUM',
                'description': '阻塞原因日志不够详细'
            })
            print("⚠️ 阻塞原因日志详细程度不足")
        else:
            print("✅ 阻塞原因日志详细程度充足")
    
    def _audit_timestamp_processor_specificity(self):
        """审计时间戳处理器中的交易所特定处理"""
        print("\n📊 4. 审计时间戳处理器中的交易所特定处理...")
        
        processor_file = self.project_root / 'websocket/unified_timestamp_processor.py'
        if not processor_file.exists():
            print("❌ unified_timestamp_processor.py 不存在")
            return
        
        try:
            with open(processor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查交易所特定处理
            exchange_specific_patterns = [
                r'if.*exchange.*==.*["\']gate["\']',
                r'if.*exchange.*==.*["\']bybit["\']',
                r'if.*exchange.*==.*["\']okx["\']',
                r'if.*self\.exchange_name.*==.*["\']gate["\']',
                r'if.*self\.exchange_name.*==.*["\']bybit["\']',
                r'if.*self\.exchange_name.*==.*["\']okx["\']'
            ]
            
            specific_handling = []
            for pattern in exchange_specific_patterns:
                matches = list(re.finditer(pattern, content, re.IGNORECASE))
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    specific_handling.append({
                        'line': line_num,
                        'match': match.group()
                    })
            
            if specific_handling:
                self.issues.append({
                    'type': '交易所特定处理',
                    'severity': 'MEDIUM',
                    'count': len(specific_handling),
                    'locations': specific_handling
                })
                print(f"⚠️ 发现 {len(specific_handling)} 处交易所特定处理")
                for handling in specific_handling:
                    print(f"   📍 第{handling['line']}行: {handling['match']}")
            else:
                print("✅ 时间戳处理器保持通用性")
        except:
            print("❌ 无法读取时间戳处理器文件")

    def _generate_fix_recommendations(self):
        """生成修复建议"""
        print("\n📊 5. 生成修复建议...")

        recommendations = []

        if self.duplicate_count > 0:
            recommendations.append({
                'priority': 'HIGH',
                'action': '清理重复的阻塞检测逻辑',
                'description': f'发现{self.duplicate_count}处重复逻辑，需要移除并统一到enhanced_blocking_tracker'
            })

        # 检查是否有交易所特定处理
        has_specific_handling = any(
            issue['type'] == '交易所特定处理'
            for issue in self.issues
        )

        if has_specific_handling:
            recommendations.append({
                'priority': 'MEDIUM',
                'action': '移除交易所特定处理',
                'description': '将交易所特定处理改为通用处理，确保系统支持任意代币'
            })

        # 检查日志详细程度
        has_logging_issues = any(
            issue['type'] == '日志详细程度不足'
            for issue in self.issues
        )

        if has_logging_issues:
            recommendations.append({
                'priority': 'MEDIUM',
                'action': '增强日志详细程度',
                'description': '确保阻塞原因日志完整，便于问题诊断'
            })

        if recommendations:
            print("🔧 修复建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. [{rec['priority']}] {rec['action']}")
                print(f"      {rec['description']}")
        else:
            print("✅ 未发现需要修复的问题")

    def _output_results(self):
        """输出诊断结果"""
        print("\n" + "=" * 80)
        print("📋 诊断结果总结")
        print("=" * 80)

        if not self.issues:
            print("🎉 恭喜！未发现任何问题，系统符合统一性要求")
            return

        # 按严重程度分类
        critical_issues = [i for i in self.issues if i.get('severity') == 'CRITICAL']
        high_issues = [i for i in self.issues if i.get('severity') == 'HIGH']
        medium_issues = [i for i in self.issues if i.get('severity') == 'MEDIUM']

        print(f"🚨 严重问题: {len(critical_issues)}")
        print(f"⚠️ 高优先级问题: {len(high_issues)}")
        print(f"📝 中优先级问题: {len(medium_issues)}")
        print(f"📊 总计问题: {len(self.issues)}")

        if self.duplicate_count > 0:
            print(f"\n🔥 重点关注: 发现 {self.duplicate_count} 处重复的阻塞检测逻辑")
            print("   这违反了'确保只有enhanced_blocking_tracker处理'的要求")

        # 保存详细结果到文件
        result_file = f"blocking_detection_audit_{int(datetime.now().timestamp())}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'duplicate_count': self.duplicate_count,
                'issues': self.issues,
                'summary': {
                    'critical': len(critical_issues),
                    'high': len(high_issues),
                    'medium': len(medium_issues),
                    'total': len(self.issues)
                }
            }, f, indent=2, ensure_ascii=False)

        print(f"\n💾 详细结果已保存到: {result_file}")

if __name__ == "__main__":
    auditor = BlockingDetectionAuditor()
    auditor.run_audit()
